
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>

    <title>Book Service - Insta Clinic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
        }

        /* Hero Section Styles */
        .hero-section {
            background-color: #1a5058;
            color: white;
            padding: 80px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        /* Service Request Form Section */
        .request-section {
            padding: 80px 20px;
            max-width: 700px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 50px;
            color: #1a5058;
            position: relative;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #00989D;
            margin: 15px auto 0;
        }

        .request-form {
            display: flex;
            flex-direction: column;
            gap: 18px;
            background-color: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 40px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #1a5058;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            font-family: "Montserrat", sans-serif;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #00989D;
            box-shadow: 0 0 0 3px rgba(0, 152, 157, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        /* Service Details Display */
        .service-details-display {
            display: none;
            background-color: #f8fffe;
            border: 2px solid #e0f4f4;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }

        .service-details-display.show {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .service-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .service-image {
            width: 100px;
            height: 100px;
            border-radius: 12px;
            object-fit: cover;
            border: 3px solid #00989D;
            box-shadow: 0 4px 12px rgba(0, 152, 157, 0.2);
            transition: all 0.3s ease;
            background-color: #f0f8f8;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .service-image:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 152, 157, 0.3);
        }

        .service-image.loading {
            background: linear-gradient(45deg, #f0f8f8, #e0f4f4, #f0f8f8);
            background-size: 200% 200%;
            animation: shimmer 1.5s infinite;
        }

        .service-image.error {
            background-color: #f9f9f9;
            border-color: #ddd;
            color: #666;
            font-size: 2rem;
        }

        @keyframes shimmer {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .service-image-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: #00989D;
            background: linear-gradient(135deg, #f0f8f8, #e0f4f4);
            border-radius: 9px;
        }

        .service-content {
            flex: 1;
        }

        .service-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1a5058;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .service-duration {
            color: #666;
            font-size: 0.95rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .service-price {
            font-size: 1.4rem;
            font-weight: 800;
            color: #00989D;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Pricing Summary */
        .pricing-summary {
            background-color: #f0f8f8;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
            border: 2px solid #d0e8e8;
        }

        .summary-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1a5058;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .summary-row:last-child {
            border-bottom: none;
            font-weight: 700;
            font-size: 1.1rem;
            color: #1a5058;
            padding-top: 15px;
        }

        .summary-label {
            color: #666;
        }

        .summary-value {
            font-weight: 600;
            color: #1a5058;
        }

        .total-price {
            color: #00989D !important;
            font-size: 1.3rem !important;
        }

        .submit-button {
            display: block;
            width: 100%;
            background-color: #00989D;
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-button:hover {
            background-color: #006868;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .submit-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* Loading State */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #00989D;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Features Section */
        .features-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-item {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            color: #00989D;
            margin-bottom: 20px;
        }

        .feature-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .request-form {
                padding: 30px 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .service-info {
                flex-direction: column;
                text-align: center;
            }

            .service-image {
                width: 100px;
                height: 100px;
            }

            .payment-methods-grid {
                grid-template-columns: 1fr;
            }

            .payment-method-section {
                padding: 20px;
            }

            .payment-method-option {
                padding: 15px;
            }
        }

        /* Error and Success Messages */
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .error-message {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }

        .success-message {
            background-color: #efe;
            color: #363;
            border: 1px solid #cfc;
        }

        /* Payment Method Selection Styles */
        .payment-method-section {
            background-color: #f8fffe;
            border: 2px solid #e0f4f4;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        .payment-method-section .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1a5058;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            text-align: left;
        }

        .payment-method-section .section-title:after {
            display: none;
        }

        .payment-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .payment-method-option {
            background-color: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .payment-method-option:hover {
            border-color: #00989D;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 152, 157, 0.1);
        }

        .payment-method-option.selected {
            border-color: #00989D;
            background-color: #f0f8f8;
            box-shadow: 0 5px 15px rgba(0, 152, 157, 0.2);
        }

        .payment-method-option.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f9f9f9;
        }

        .payment-method-option.disabled:hover {
            transform: none;
            box-shadow: none;
            border-color: #e0e0e0;
        }

        .payment-method-icon {
            font-size: 2rem;
            color: #00989D;
            min-width: 40px;
            text-align: center;
        }

        .payment-method-info {
            flex: 1;
        }

        .payment-method-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1a5058;
            margin-bottom: 5px;
        }

        .payment-method-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .payment-method-radio {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-radius: 50%;
            background-color: white;
            transition: all 0.3s ease;
        }

        .payment-method-option.selected .payment-method-radio {
            border-color: #00989D;
            background-color: #00989D;
        }

        .payment-method-option.selected .payment-method-radio::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: white;
        }

        .payment-method-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #00989D;
            color: white;
            font-size: 0.7rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .payment-method-option.disabled .payment-method-badge {
            background-color: #ccc;
        }
    </style>
</head>

<div id="header"></div>

<body>
    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Book Your Service</h1>
                <p class="hero-description">Schedule your appointment with Insta Clinic. Simply fill out the form below and we'll confirm your booking within 24 hours.</p>
            </div>
        </section>

        <!-- Service Request Form Section -->
        <section class="request-section">
            <h2 class="section-title">Request Your Service</h2>
            <p style="text-align: center; max-width: 600px; margin: 0 auto; color: #666; line-height: 1.6;">
                Please provide your details below to book your appointment. All information is kept confidential and secure.
            </p>

            <form class="request-form" id="serviceRequestForm">
                <div id="messageContainer"></div>

                <!-- Service Selection -->
                <div class="form-group full-width">
                    <label for="serviceType">Service Type *</label>
                    <select id="serviceType" name="serviceType" required>
                        <option value="">Loading services...</option>
                    </select>
                </div>

                <!-- Service Details Display -->
                <div id="serviceDetailsDisplay" class="service-details-display">
                    <div class="service-info">
                        <div id="serviceImage" class="service-image">
                            <div class="service-image-placeholder">
                                <i class="fas fa-image"></i>
                            </div>
                        </div>
                        <div class="service-content">
                            <div id="serviceName" class="service-name"></div>
                            <div id="serviceDuration" class="service-duration"></div>
                            <div id="servicePrice" class="service-price"></div>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">First Name *</label>
                        <input type="text" id="firstName" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="lastName">Last Name *</label>
                        <input type="text" id="lastName" name="lastName" required>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="form-group full-width">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" placeholder="e.g., ************" required>
                </div>

                <!-- Address -->
                <div class="form-group full-width">
                    <label for="address">Live Address (Service Address)*</label>
                    <textarea id="address" name="address" placeholder="Please enter your service address here..." required></textarea>
                </div>

                <!-- Date and Time -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="preferredDate">Preferred Date *</label>
                        <input type="date" id="preferredDate" name="preferredDate" required>
                    </div>
                    <div class="form-group">
                        <label for="preferredTime">Preferred Time Range *</label>
                        <select id="preferredTime" name="preferredTime" required>
                            <option value="">Select time range</option>
                            <option value="08:00-10:00">8:00 AM - 10:00 AM</option>
                            <option value="10:00-12:00">10:00 AM - 12:00 PM</option>
                            <option value="12:00-14:00">12:00 PM - 2:00 PM</option>
                            <option value="14:00-16:00">2:00 PM - 4:00 PM</option>
                            <option value="16:00-18:00">4:00 PM - 6:00 PM</option>
                            <option value="18:00-20:00">6:00 PM - 8:00 PM</option>
                            <option value="20:00-22:00">8:00 PM - 10:00 PM</option>
                        </select>
                    </div>
                </div>

                <!-- Additional Notes (Optional) -->
                <div class="form-group full-width">
                    <label for="notes">Additional Notes (Optional)</label>
                    <textarea id="notes" name="notes" placeholder="Please mention any specific requirements, symptoms, or additional information..."></textarea>
                </div>

                <!-- Pricing Summary -->
                <div id="pricingSummary" class="pricing-summary" style="display: none;">
                    <div class="summary-title">
                        <i class="fas fa-receipt"></i>
                        Booking Summary
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Service:</span>
                        <span id="summaryServiceName" class="summary-value">-</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Duration:</span>
                        <span id="summaryDuration" class="summary-value">-</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Date & Time:</span>
                        <span id="summaryDateTime" class="summary-value">-</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Total Price:</span>
                        <span id="summaryPrice" class="summary-value total-price">$0.00</span>
                    </div>
                </div>

                <!-- Payment Method Selection -->
                <div id="paymentMethodSection" class="payment-method-section" style="display: none;">
                    <div class="section-title">
                        <i class="fas fa-credit-card"></i>
                        Choose Payment Method
                    </div>
                    <div class="payment-methods-grid" id="paymentMethodsGrid">
                        <!-- Payment methods will be dynamically loaded here -->
                    </div>
                </div>

                <button type="submit" class="submit-button">Book Appointment</button>
            </form>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="features-container">
                <h2 class="section-title">Why Choose Insta Clinic</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    Experience professional healthcare services with the convenience and comfort you deserve.
                </p>

                <div class="features-grid">
                    <div class="feature-item">
                        <i class="fas fa-home feature-icon"></i>
                        <h3 class="feature-title">Home Service Available</h3>
                        <p class="feature-description">Many of our services can be provided at your home for maximum convenience and comfort.</p>
                    </div>

                    <div class="feature-item">
                        <i class="fas fa-user-md feature-icon"></i>
                        <h3 class="feature-title">Expert Professionals</h3>
                        <p class="feature-description">Our team of qualified doctors and healthcare professionals ensure you receive the highest quality care.</p>
                    </div>

                    <div class="feature-item">
                        <i class="fas fa-clock feature-icon"></i>
                        <h3 class="feature-title">Flexible Scheduling</h3>
                        <p class="feature-description">Book appointments at times that work for you, including weekends and evening slots.</p>
                    </div>

                    <div class="feature-item">
                        <i class="fas fa-shield-alt feature-icon"></i>
                        <h3 class="feature-title">Safe & Secure</h3>
                        <p class="feature-description">All services follow strict safety protocols and your personal information is kept completely confidential.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer"></div>

    <!-- Add this within an iframe instead of redirecting -->
    <div id="payment-iframe-container" style="display: none;">
        <iframe id="payment-iframe" width="100%" height="600px" frameborder="0"></iframe>
    </div>

    <script>
        // Supabase configuration (loaded from config.js)
        const SUPABASE_URL = window.SUPABASE_CONFIG?.url || 'MISSING_CONFIG';
        const SUPABASE_ANON_KEY = window.SUPABASE_CONFIG?.anonKey || 'MISSING_CONFIG';
        
        // PayMob configuration - Add these to your config.js file
        const PAYMOB_API_KEY = window.PAYMOB_CONFIG?.apiKey || 'MISSING_CONFIG';
        const PAYMOB_INTEGRATION_ID = window.PAYMOB_CONFIG?.integrationId || 'MISSING_CONFIG';
        const PAYMOB_IFRAME_ID = window.PAYMOB_CONFIG?.iframeId || 'MISSING_CONFIG';
        const PAYMOB_HMAC_SECRET = window.PAYMOB_CONFIG?.hmacSecret || 'MISSING_CONFIG';
        
        // Check if config is loaded properly
        if (SUPABASE_URL === 'MISSING_CONFIG' || SUPABASE_ANON_KEY === 'MISSING_CONFIG') {
            console.error('Supabase configuration not loaded! Please ensure config.js exists and is properly configured.');
        }
        
        if (PAYMOB_API_KEY === 'MISSING_CONFIG' || PAYMOB_INTEGRATION_ID === 'MISSING_CONFIG') {
            console.error('PayMob configuration not loaded! Please ensure config.js exists and is properly configured.');
        }
        
        // Initialize Supabase client
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Global variables
        let services = [];
        let selectedService = null;
        let selectedPaymentMethod = null;
        let paymentMethods = {};

        // Function to load the header and footer
        window.onload = function loadContent() {
            // Load header
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                })
                .catch(error => console.error('Error loading header:', error));

            // Load footer
            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading footer:', error));

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('preferredDate').setAttribute('min', today);

            // Load services from Supabase
            loadServices();
            
            // Initialize payment methods
            initializePaymentMethods();
        }

        // Function to load services from Supabase
        async function loadServices() {
            const serviceSelect = document.getElementById('serviceType');
            
            try {
                const { data, error } = await supabase
                    .from('services')
                    .select('*')
                    .order('name');

                if (error) {
                    throw error;
                }

                services = data;
                populateServiceDropdown();
                
            } catch (error) {
                console.error('Error loading services:', error);
                serviceSelect.innerHTML = '<option value="">Error loading services</option>';
                showMessage('Failed to load services. Please refresh the page.', 'error');
            }
        }

        // Function to populate service dropdown
        function populateServiceDropdown() {
            const serviceSelect = document.getElementById('serviceType');
            serviceSelect.innerHTML = '<option value="">Select the service you need</option>';
            
            services.forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.name} - ${service.price} EGP`;
                serviceSelect.appendChild(option);
            });
        }

        // Function to initialize payment methods
        function initializePaymentMethods() {
            try {
                // Get payment methods from config
                paymentMethods = window.PAYMENT_METHODS || {};
                
                // Load payment methods UI
                loadPaymentMethods();
                
                console.log('Payment methods initialized:', paymentMethods);
            } catch (error) {
                console.error('Error initializing payment methods:', error);
            }
        }

        // Function to load payment methods UI
        function loadPaymentMethods() {
            const paymentMethodsGrid = document.getElementById('paymentMethodsGrid');
            paymentMethodsGrid.innerHTML = '';
            
            Object.values(paymentMethods).forEach(method => {
                const methodElement = createPaymentMethodElement(method);
                paymentMethodsGrid.appendChild(methodElement);
            });
        }

        // Function to create payment method element
        function createPaymentMethodElement(method) {
            const methodDiv = document.createElement('div');
            methodDiv.className = `payment-method-option ${!method.enabled ? 'disabled' : ''}`;
            methodDiv.dataset.methodId = method.id;
            
            if (method.enabled) {
                methodDiv.addEventListener('click', () => selectPaymentMethod(method.id));
            }
            
            const badge = method.enabled ? '' : '<div class="payment-method-badge">Coming Soon</div>';
            
            methodDiv.innerHTML = `
                <div class="payment-method-radio"></div>
                ${badge}
                <div class="payment-method-icon">
                    <i class="${method.icon}"></i>
                </div>
                <div class="payment-method-info">
                    <div class="payment-method-name">${method.name}</div>
                    <div class="payment-method-description">${method.description}</div>
                </div>
            `;
            
            return methodDiv;
        }

        // Function to select payment method
        function selectPaymentMethod(methodId) {
            // Remove previous selection
            document.querySelectorAll('.payment-method-option').forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selection to clicked method
            const selectedElement = document.querySelector(`[data-method-id="${methodId}"]`);
            if (selectedElement) {
                selectedElement.classList.add('selected');
            }
            
            // Store selected method
            selectedPaymentMethod = paymentMethods[methodId];
            
            console.log('Selected payment method:', selectedPaymentMethod);
        }

        // Function to show payment method section
        function showPaymentMethodSection() {
            const paymentSection = document.getElementById('paymentMethodSection');
            paymentSection.style.display = 'block';
            
            // Auto-select first enabled payment method if none selected
            if (!selectedPaymentMethod) {
                const firstEnabledMethod = Object.values(paymentMethods).find(method => method.enabled);
                if (firstEnabledMethod) {
                    selectPaymentMethod(firstEnabledMethod.id);
                }
            }
        }

        // Function to hide payment method section
        function hidePaymentMethodSection() {
            const paymentSection = document.getElementById('paymentMethodSection');
            paymentSection.style.display = 'none';
            selectedPaymentMethod = null;
        }

        // Function to display service details
        function displayServiceDetails(service) {
            const serviceDisplay = document.getElementById('serviceDetailsDisplay');
            const serviceImage = document.getElementById('serviceImage');
            const serviceName = document.getElementById('serviceName');
            const serviceDuration = document.getElementById('serviceDuration');
            const servicePrice = document.getElementById('servicePrice');

            if (service) {
                // Set loading state
                serviceImage.className = 'service-image loading';
                serviceImage.innerHTML = '<div class="service-image-placeholder"><i class="fas fa-spinner fa-spin"></i></div>';
                
                serviceName.textContent = service.name;
                serviceDuration.innerHTML = `<i class="fas fa-clock"></i> Duration: ${service.duration_minutes} minutes`;
                servicePrice.innerHTML = `${parseFloat(service.price).toFixed(2)} EGP`;
                
                serviceDisplay.classList.add('show');
                selectedService = service;
                
                // Handle image loading
                loadServiceImage(service, serviceImage);
                updatePricingSummary();
                
                // Show payment method section
                showPaymentMethodSection();
            } else {
                serviceDisplay.classList.remove('show');
                selectedService = null;
                hidePricingSummary();
            }
        }

        // Function to load service image with fallback
        function loadServiceImage(service, imageContainer) {
            // Set loading state
            imageContainer.className = 'service-image loading';
            imageContainer.innerHTML = '<div class="service-image-placeholder"><i class="fas fa-spinner fa-spin"></i></div>';
            
            if (service.image_url && service.image_url.trim() !== '') {
                const img = new Image();
                
                img.onload = function() {
                    // Image loaded successfully
                    imageContainer.className = 'service-image';
                    imageContainer.innerHTML = `<img src="${service.image_url}" alt="${service.name}" style="width: 80%; height: 80%; object-fit: fill; border-radius: 9px;">`;
                };
                
                img.onerror = function() {
                    // Image failed to load, use fallback
                    console.warn(`Failed to load image for ${service.name}: ${service.image_url}`);
                    setFallbackImage(imageContainer, service);
                };
                
                // Start loading
                img.src = service.image_url;
                
                // Timeout fallback (in case image takes too long)
                setTimeout(() => {
                    if (imageContainer.className.includes('loading')) {
                        console.warn(`Image loading timeout for ${service.name}`);
                        setFallbackImage(imageContainer, service);
                    }
                }, 5000); // 5 second timeout
                
            } else {
                // No image URL provided, use fallback immediately
                setFallbackImage(imageContainer, service);
            }
        }

        // Function to set fallback image based on service type
        function setFallbackImage(imageContainer, service) {
            imageContainer.className = 'service-image';
            
            // Get appropriate icon based on service name
            const serviceIcon = getServiceIcon(service.name);
            
            imageContainer.innerHTML = `
                <div class="service-image-placeholder">
                    <i class="fas ${serviceIcon}"></i>
                </div>
            `;
        }

        // Function to get appropriate icon for service type
        function getServiceIcon(serviceName) {
            const name = serviceName.toLowerCase();
            
            if (name.includes('dental') || name.includes('tooth')) return 'fa-tooth';
            if (name.includes('heart') || name.includes('cardio')) return 'fa-heartbeat';
            if (name.includes('eye') || name.includes('vision')) return 'fa-eye';
            if (name.includes('brain') || name.includes('neuro')) return 'fa-brain';
            if (name.includes('bone') || name.includes('orthopedic')) return 'fa-bone';
            if (name.includes('baby') || name.includes('pediatric') || name.includes('child')) return 'fa-baby';
            if (name.includes('emergency') || name.includes('urgent')) return 'fa-ambulance';
            if (name.includes('surgery') || name.includes('surgical')) return 'fa-procedures';
            if (name.includes('lab') || name.includes('test') || name.includes('blood')) return 'fa-flask';
            if (name.includes('radio') || name.includes('x-ray') || name.includes('scan')) return 'fa-x-ray';
            if (name.includes('physio') || name.includes('therapy')) return 'fa-dumbbell';
            if (name.includes('nursing') || name.includes('nurse')) return 'fa-user-nurse';
            if (name.includes('derma') || name.includes('skin')) return 'fa-hand-holding-medical';
            if (name.includes('dialysis') || name.includes('kidney')) return 'fa-lungs';
            if (name.includes('animal') || name.includes('vet') || name.includes('pet')) return 'fa-paw';
            if (name.includes('medicine') || name.includes('general')) return 'fa-stethoscope';
            
            // Default medical icon
            return 'fa-user-md';
        }

        // Test function for fallback images (for debugging)
        function testFallback() {
            const testService = {
                name: 'Test Dental Service',
                price: 50,
                duration_minutes: 30,
                image_url: 'https://invalid-url-that-will-fail.com/image.jpg'
            };
            
            displayServiceDetails(testService);
            console.log('Testing fallback with invalid image URL...');
        }

        // Function to update pricing summary
        function updatePricingSummary() {
            if (!selectedService) {
                hidePricingSummary();
                return;
            }

            const pricingSummary = document.getElementById('pricingSummary');
            const summaryServiceName = document.getElementById('summaryServiceName');
            const summaryDuration = document.getElementById('summaryDuration');
            const summaryDateTime = document.getElementById('summaryDateTime');
            const summaryPrice = document.getElementById('summaryPrice');

            summaryServiceName.textContent = selectedService.name;
            summaryDuration.textContent = `${selectedService.duration_minutes} minutes`;
            summaryPrice.textContent = `${parseFloat(selectedService.price).toFixed(2)} EGP`;

            // Update date and time if available
            const date = document.getElementById('preferredDate').value;
            const time = document.getElementById('preferredTime').value;
            
            if (date && time) {
                const formattedDate = new Date(date).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                const formattedTime = formatTime(time);
                summaryDateTime.textContent = `${formattedDate} at ${formattedTime}`;
            } else {
                summaryDateTime.textContent = 'To be selected';
            }

            pricingSummary.style.display = 'block';
        }

        // Function to hide pricing summary
        function hidePricingSummary() {
            document.getElementById('pricingSummary').style.display = 'none';
            hidePaymentMethodSection();
        }

        // Function to format time range
        function formatTime(timeRange) {
            if (!timeRange || !timeRange.includes('-')) {
                return timeRange; // Return as-is if not a range
            }
            
            const [startTime, endTime] = timeRange.split('-');
            
            function formatSingleTime(time24) {
                const [hours, minutes] = time24.split(':');
                const hour = parseInt(hours);
                const ampm = hour >= 12 ? 'PM' : 'AM';
                const hour12 = hour % 12 || 12;
                const formattedHour = hour12.toString().padStart(2, '0');
                return `${formattedHour}:${minutes || '00'} ${ampm}`;
            }
            
            const formattedStart = formatSingleTime(startTime);
            const formattedEnd = formatSingleTime(endTime);
            
            return `${formattedStart} - ${formattedEnd}`;
        }

        // Event listeners
        document.getElementById('serviceType').addEventListener('change', function() {
            const selectedServiceId = this.value;
            if (selectedServiceId) {
                const service = services.find(s => s.id == selectedServiceId);
                displayServiceDetails(service);
            } else {
                displayServiceDetails(null);
            }
        });

        // Update pricing summary when date/time changes
        document.getElementById('preferredDate').addEventListener('change', updatePricingSummary);
        document.getElementById('preferredTime').addEventListener('change', updatePricingSummary);

        // Form validation and submission
        document.getElementById('serviceRequestForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const messageContainer = document.getElementById('messageContainer');
            const submitButton = this.querySelector('.submit-button');
            
            // Clear previous messages
            messageContainer.innerHTML = '';
            
            // Disable submit button during processing
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading"></span> Processing...';
            
            try {
                // Get form data
                const formData = new FormData(this);
                const serviceType = formData.get('serviceType');
                const firstName = formData.get('firstName');
                const lastName = formData.get('lastName');
                const phone = formData.get('phone');
                const address = formData.get('address');
                const preferredDate = formData.get('preferredDate');
                const preferredTime = formData.get('preferredTime');
                const notes = formData.get('notes') || '';
                
                // Validate required fields
                if (!serviceType || !firstName || !lastName || !phone || !address || !preferredDate || !preferredTime) {
                    showMessage('Please fill in all required fields.', 'error');
                    return;
                }
                
                // Validate service is selected
                if (!selectedService) {
                    showMessage('Please select a valid service.', 'error');
                    return;
                }
                
                // Validate payment method is selected
                if (!selectedPaymentMethod) {
                    showMessage('Please select a payment method.', 'error');
                    return;
                }
                
                // Validate phone number format (basic validation)
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                if (!phoneRegex.test(phone.replace(/[\s\(\)\-]/g, ''))) {
                    showMessage('Please enter a valid phone number.', 'error');
                    return;
                }
                
                // Validate date is not in the past
                const selectedDate = new Date(preferredDate);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                if (selectedDate < today) {
                    showMessage('Please select a date that is today or in the future.', 'error');
                    return;
                }
                
                // Prepare booking data for database
                const bookingData = {
                    patient_name: `${firstName.trim()} ${lastName.trim()}`,
                    patient_address: address.trim(),
                    contact_number: phone.trim(),
                    notes: notes.trim(),
                    booking_date: preferredDate,
                    booking_time: preferredTime,
                    status: 'pending_payment', // Changed from 'unpaid' to 'pending_payment'
                    fee: parseFloat(selectedService.price),
                    service_id: selectedService.id
                };
                
                console.log('Inserting booking data:', bookingData);
                
                // Insert booking into Supabase
                const { data: bookingResult, error } = await supabase
                    .from('bookings')
                    .insert([bookingData])
                    .select();
                
                if (error) {
                    throw error;
                }
                
                console.log('Booking created successfully:', bookingResult);
                
                const bookingId = bookingResult[0].id;
                
                // Store booking details for payment callback
                const paymentData = {
                    bookingId: bookingId,
                    serviceName: selectedService.name,
                    patientName: bookingData.patient_name,
                    appointmentDate: preferredDate,
                    appointmentTime: preferredTime,
                    amount: selectedService.price,
                    paymentMethod: selectedPaymentMethod.id
                };
                
                sessionStorage.setItem('currentBooking', JSON.stringify(paymentData));
                
                // Initialize PayMob payment process with selected payment method
                await initiatePayMobPayment(paymentData, selectedPaymentMethod);
                
            } catch (error) {
                console.error('Error creating booking:', error);
                
                // Show user-friendly error message
                let errorMessage = 'Failed to initiate payment. Please try again.';
                
                if (error.message) {
                    // Handle specific errors
                    if (error.message.includes('blocked by browser extension') || error.message.includes('ERR_BLOCKED_BY_CLIENT')) {
                        errorMessage = 'Payment request blocked by ad blocker or browser extension. Please disable ad blockers for this site and try again.';
                    } else if (error.message.includes('duplicate')) {
                        errorMessage = 'A booking with these details already exists. Please check your information.';
                    } else if (error.message.includes('permission')) {
                        errorMessage = 'Permission denied. Please contact support.';
                    } else if (error.message.includes('network')) {
                        errorMessage = 'Network error. Please check your internet connection and try again.';
                    }
                }
                
                showMessage(errorMessage, 'error');
                
            } finally {
                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Book Appointment';
            }
        });
        
        function showMessage(message, type) {
            const messageContainer = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = message;
            messageContainer.appendChild(messageDiv);
            
            // Scroll to message
            messageDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // PayMob integration functions
        async function initiatePayMobPayment(bookingData, paymentMethodConfig) {
            try {
                // Use the selected payment method configuration
                const config = paymentMethodConfig.config;
                
                // Step 1: Get authentication token
                const authToken = await getPayMobAuthToken(config);
                
                // Step 2: Create order
                const orderId = await createPayMobOrder(authToken, bookingData, config);
                
                // Step 3: Get payment key
                const paymentKey = await getPayMobPaymentKey(authToken, orderId, bookingData, config);
                
                // Step 4: Redirect to PayMob iframe
                redirectToPayMobIframe(paymentKey, config);
                
            } catch (error) {
                console.error('PayMob payment initiation failed:', error);
                showMessage('Payment initialization failed. Please try again.', 'error');
            }
        }

        async function getPayMobAuthToken(config) {
            console.log('Requesting PayMob auth token...');
            console.log('Using API key:', config.apiKey ? config.apiKey.substring(0, 20) + '...' : 'undefined');
            console.log('Config details:', {
                integrationId: config.integrationId,
                iframeId: config.iframeId,
                ucoIntegrationId: config.ucoIntegrationId
            });
            
            try {
                const response = await fetch('https://accept.paymob.com/api/auth/tokens', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_key: config.apiKey
                    })
                });
                
                console.log('Auth token response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Auth token error response:', errorText);
                    throw new Error('Failed to get PayMob auth token: ' + response.status + ' ' + response.statusText);
                }
                
                const data = await response.json();
                console.log('Auth token received successfully');
                console.log('Token preview:', data.token ? data.token.substring(0, 20) + '...' : 'undefined');
                return data.token;
                
            } catch (error) {
                console.error('Auth token request failed:', error);
                
                // Check if it's a blocked request
                if (error.message.includes('ERR_BLOCKED_BY_CLIENT') || error.name === 'TypeError') {
                    throw new Error('Request blocked by browser extension or ad blocker. Please disable ad blockers and try again.');
                }
                
                throw error;
            }
        }

        async function createPayMobOrder(authToken, bookingData, config) {
            const amountCents = Math.round(parseFloat(bookingData.amount) * 100); // Convert to cents
            
            console.log('Creating PayMob order...', { amountCents, serviceName: bookingData.serviceName });
            
            try {
                const response = await fetch('https://accept.paymob.com/api/ecommerce/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        auth_token: authToken,
                        delivery_needed: "false",
                        amount_cents: amountCents,
                        currency: "EGP",
                        items: [{
                            name: bookingData.serviceName,
                            amount_cents: amountCents,
                            description: `Medical appointment booking - ${bookingData.serviceName} (${config === window.PAYMOB_CONFIG ? 'Standard' : 'Alternative'} Payment)`,
                            quantity: 1
                        }]
                    })
                });
                
                console.log('Order creation response status:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Order creation error response:', errorText);
                    throw new Error('Failed to create PayMob order: ' + response.status + ' ' + response.statusText);
                }
                
                const data = await response.json();
                console.log('Order created successfully:', data.id);
                return data.id;
                
            } catch (error) {
                console.error('Order creation request failed:', error);
                
                if (error.message.includes('ERR_BLOCKED_BY_CLIENT') || error.name === 'TypeError') {
                    throw new Error('Request blocked by browser extension or ad blocker. Please disable ad blockers and try again.');
                }
                
                throw error;
            }
        }

        async function getPayMobPaymentKey(authToken, orderId, bookingData, config) {
            const amountCents = Math.round(parseFloat(bookingData.amount) * 100);
            
            // Get contact number from current form if available
            const contactNumber = document.getElementById('phone')?.value || "+20XXXXXXXXX";
            
            // Determine which integration ID to use
            const isAlternative = bookingData?.paymentMethod === 'alternative';
            const integrationId = config.integrationId; // Always use the main integration ID

            console.log('Payment key generation details:', {
                isAlternative,
                paymentMethod: bookingData?.paymentMethod,
                integrationId,
                configIntegrationId: config.integrationId,
                configUcoIntegrationId: config.ucoIntegrationId
            });
            
            console.log('Payment key request details:', {
                isAlternative,
                ucoIntegrationId: config.ucoIntegrationId,
                standardIntegrationId: config.integrationId,
                selectedIntegrationId: integrationId,
                amountCents,
                contactNumber,
                bookingAmount: bookingData.amount
            });
            
            const paymentKeyData = {
                auth_token: authToken,
                amount_cents: amountCents,
                expiration: 3600, // 1 hour
                order_id: orderId,
                billing_data: {
                    apartment: "NA",
                    email: "<EMAIL>", // You may want to add email field to your form
                    floor: "NA",
                    first_name: bookingData.patientName.split(' ')[0],
                    street: "NA",
                    building: "NA",
                    phone_number: contactNumber,
                    shipping_method: "NA",
                    postal_code: "NA",
                    city: "Cairo",
                    country: "EG",
                    last_name: bookingData.patientName.split(' ').slice(1).join(' ') || "N/A",
                    state: "Cairo"
                },
                currency: "EGP",
                integration_id: integrationId,
                lock_order_when_paid: "false"
            };

            // Add specific parameters for Forsa/alternative payment method
            if (isAlternative) {
                // Ensure we're using the correct integration ID for Forsa
                paymentKeyData.integration_id = config.integrationId; // Use the Forsa integration ID
                paymentKeyData.special_reference = `FORSA_${orderId}_${Date.now()}`;

                // Add merchant order ID for better tracking
                paymentKeyData.merchant_order_id = `BOOKING_${bookingData.bookingId}_${Date.now()}`;

                // Ensure billing data has all required fields for Forsa
                paymentKeyData.billing_data.phone_number = contactNumber.replace(/\s+/g, ''); // Remove spaces
                paymentKeyData.billing_data.email = "<EMAIL>"; // Use a proper email

                console.log('Forsa payment key data:', JSON.stringify(paymentKeyData, null, 2));
            }
            
            console.log('Payment key request payload:', paymentKeyData);
            
            const response = await fetch('https://accept.paymob.com/api/acceptance/payment_keys', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentKeyData)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                console.error('PayMob payment key error:', {
                    status: response.status,
                    statusText: response.statusText,
                    error: data,
                    requestData: paymentKeyData
                });
                throw new Error('Failed to get PayMob payment key: ' + (data.message || response.statusText || 'Unknown error'));
            }
            
            console.log('Payment key generated successfully:', data.token);
            console.log('Payment key response data:', data);

            // Validate the payment key
            if (!data.token || data.token.length < 10) {
                throw new Error('Invalid payment key received from PayMob');
            }

            // Additional debugging for Mobile Wallet
            if (isAlternative) {
                console.log('Mobile Wallet payment key details:', {
                    tokenLength: data.token.length,
                    integrationUsed: integrationId,
                    orderAmount: amountCents,
                    phoneNumber: contactNumber
                });
            }

            return data.token;
        }

        function redirectToPayMobIframe(paymentKey, config) {
            // Store the current booking data for callback processing
            const currentBooking = JSON.parse(sessionStorage.getItem('currentBooking'));
            
            // Add additional metadata for callback processing
            const enhancedBookingData = {
                ...currentBooking,
                paymentKey: paymentKey,
                paymentConfig: config,
                timestamp: new Date().toISOString()
            };
            
            sessionStorage.setItem('currentBooking', JSON.stringify(enhancedBookingData));
            
            // Create the PayMob URL based on selected payment method
            // For Forsa/alternative, use unified checkout which prompts for mobile number
            const isAlternative = enhancedBookingData?.paymentMethod === 'alternative';
            
            // Enhanced URL construction with proper parameters for Forsa
            let iframeUrl;
            if (isAlternative) {
                // For Mobile Wallet (Forsa), try iframe approach first since unified checkout is not working
                const callbackUrl = encodeURIComponent(window.PAYMOB_CALLBACK_URLS?.callbackUrl || `${window.location.origin}/paymob-callback.html`);

                // For Forsa, use iframe approach (more reliable than unified checkout)
                iframeUrl = `https://accept.paymob.com/api/acceptance/iframes/${config.iframeId}?payment_token=${paymentKey}`;
                console.log('=== FORSA PAYMENT DEBUG ===');
                console.log('Using Forsa iframe approach');
                console.log('Forsa iframe ID:', config.iframeId);
                console.log('Forsa integration ID:', config.integrationId);

                console.log('Mobile Wallet payment URL constructed:', iframeUrl);
                console.log('Callback URL:', callbackUrl);
                console.log('Integration ID used in payment key:', config.integrationId);
                console.log('Iframe ID used:', config.iframeId);
                console.log('Payment token preview:', paymentKey.substring(0, 50) + '...');

                // Debug: Check if we're using the right config
                console.log('Mobile Wallet config check:', {
                    apiKey: config.apiKey ? config.apiKey.substring(0, 20) + '...' : 'missing',
                    integrationId: config.integrationId,
                    iframeId: config.iframeId,
                    configName: config.name || 'Mobile Wallet'
                });

                // Additional debug: Check payment token validity
                if (!paymentKey || paymentKey.length < 50) {
                    console.error('Invalid payment token for Mobile Wallet:', paymentKey);
                }
            } else {
                // For standard payment, use iframe
                iframeUrl = `https://accept.paymob.com/api/acceptance/iframes/${config.iframeId}?payment_token=${paymentKey}`;
            }
            
            console.log('Redirecting to PayMob iframe:', iframeUrl);
            console.log('Using payment method config:', config);
            console.log('Enhanced booking data:', enhancedBookingData);
            console.log('Payment key used:', paymentKey);

            // Add a longer delay to ensure all data is logged and visible
            console.log('=== FINAL DEBUG INFO ===');
            console.log('Final iframe URL:', iframeUrl);
            console.log('Payment key length:', paymentKey ? paymentKey.length : 'MISSING');
            console.log('Is alternative payment:', isAlternative);
            console.log('Config integration ID:', config.integrationId);
            console.log('Config iframe ID:', config.iframeId);
            console.log('Redirecting in 3 seconds...');

            setTimeout(() => {
                try {
                    // Store additional debug info
                    sessionStorage.setItem('paymentDebugInfo', JSON.stringify({
                        paymentKey: paymentKey,
                        iframeUrl: iframeUrl,
                        isAlternative: isAlternative,
                        config: {
                            integrationId: config.integrationId,
                            ucoIntegrationId: config.ucoIntegrationId,
                            iframeId: config.iframeId
                        },
                        timestamp: new Date().toISOString()
                    }));

                    console.log('Redirecting now to:', iframeUrl);
                    // Direct redirect to PayMob payment page
                    window.location.href = iframeUrl;
                } catch (error) {
                    console.error('Error during redirect:', error);
                    showMessage('Failed to redirect to payment page. Please try again.', 'error');
                }
            }, 3000);
        }
    </script>
</body>

</html> 
