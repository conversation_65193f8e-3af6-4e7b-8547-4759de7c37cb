<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forsa Payment Test - Insta Clinic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #1a5058;
            margin-top: 0;
        }
        button {
            background-color: #00989D;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #006868;
        }
        .log-area {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .info {
            color: blue;
        }
        input, select {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Forsa Payment Integration Test</h1>
        <p>This page helps debug Forsa payment integration issues.</p>

        <div class="test-section">
            <h3>Test Configuration</h3>
            <label for="testAmount">Test Amount (EGP):</label>
            <input type="number" id="testAmount" value="100" min="1">
            
            <label for="testPhone">Test Phone Number:</label>
            <input type="tel" id="testPhone" value="+201234567890">
            
            <label for="testName">Test Customer Name:</label>
            <input type="text" id="testName" value="Test Customer">
        </div>

        <div class="test-section">
            <h3>Configuration Check</h3>
            <button onclick="checkConfiguration()">Check PayMob Configuration</button>
            <div id="configResult" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>Authentication Test</h3>
            <button onclick="testAuthentication()">Test Auth Token (Standard)</button>
            <button onclick="testAuthenticationForsa()">Test Auth Token (Forsa)</button>
            <div id="authResult" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>Order Creation Test</h3>
            <button onclick="testOrderCreation()">Test Order Creation (Standard)</button>
            <button onclick="testOrderCreationForsa()">Test Order Creation (Forsa)</button>
            <div id="orderResult" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>Payment Key Test</h3>
            <button onclick="testPaymentKey()">Test Payment Key (Standard)</button>
            <button onclick="testPaymentKeyForsa()">Test Payment Key (Forsa)</button>
            <div id="paymentKeyResult" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>Full Payment Flow Test</h3>
            <button onclick="testFullFlowStandard()">Test Full Flow (Standard)</button>
            <button onclick="testFullFlowForsa()">Test Full Flow (Forsa)</button>
            <div id="fullFlowResult" class="log-area"></div>
        </div>
    </div>

    <script src="config.js"></script>
    <script>
        let currentAuthToken = null;
        let currentOrderId = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkConfiguration() {
            clearLog('configResult');
            log('configResult', 'Checking PayMob configuration...', 'info');
            
            // Check standard config
            log('configResult', 'Standard Payment Config:', 'info');
            log('configResult', `  API Key: ${window.PAYMOB_CONFIG?.apiKey ? 'Present (' + window.PAYMOB_CONFIG.apiKey.substring(0, 20) + '...)' : 'Missing'}`, window.PAYMOB_CONFIG?.apiKey ? 'success' : 'error');
            log('configResult', `  Integration ID: ${window.PAYMOB_CONFIG?.integrationId || 'Missing'}`, window.PAYMOB_CONFIG?.integrationId ? 'success' : 'error');
            log('configResult', `  Iframe ID: ${window.PAYMOB_CONFIG?.iframeId || 'Missing'}`, window.PAYMOB_CONFIG?.iframeId ? 'success' : 'error');
            
            // Check Forsa config
            log('configResult', '\nForsa Payment Config:', 'info');
            log('configResult', `  API Key: ${window.PAYMOB_CONFIG_ALT?.apiKey ? 'Present (' + window.PAYMOB_CONFIG_ALT.apiKey.substring(0, 20) + '...)' : 'Missing'}`, window.PAYMOB_CONFIG_ALT?.apiKey ? 'success' : 'error');
            log('configResult', `  Integration ID: ${window.PAYMOB_CONFIG_ALT?.integrationId || 'Missing'}`, window.PAYMOB_CONFIG_ALT?.integrationId ? 'success' : 'error');
            log('configResult', `  UCO Integration ID: ${window.PAYMOB_CONFIG_ALT?.ucoIntegrationId || 'Missing'}`, window.PAYMOB_CONFIG_ALT?.ucoIntegrationId ? 'success' : 'error');
            log('configResult', `  Iframe ID: ${window.PAYMOB_CONFIG_ALT?.iframeId || 'Missing'}`, window.PAYMOB_CONFIG_ALT?.iframeId ? 'success' : 'error');
            
            // Check callback URLs
            log('configResult', '\nCallback URLs:', 'info');
            log('configResult', `  Callback URL: ${window.PAYMOB_CALLBACK_URLS?.callbackUrl || 'Missing'}`, window.PAYMOB_CALLBACK_URLS?.callbackUrl ? 'success' : 'error');
            log('configResult', `  Success URL: ${window.PAYMOB_CALLBACK_URLS?.successPageUrl || 'Missing'}`, window.PAYMOB_CALLBACK_URLS?.successPageUrl ? 'success' : 'error');
            log('configResult', `  Failure URL: ${window.PAYMOB_CALLBACK_URLS?.failurePageUrl || 'Missing'}`, window.PAYMOB_CALLBACK_URLS?.failurePageUrl ? 'success' : 'error');
        }

        async function testAuthentication() {
            clearLog('authResult');
            log('authResult', 'Testing authentication with standard config...', 'info');
            
            try {
                const response = await fetch('https://accept.paymob.com/api/auth/tokens', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_key: window.PAYMOB_CONFIG.apiKey
                    })
                });
                
                log('authResult', `Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log('authResult', `Error response: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                currentAuthToken = data.token;
                log('authResult', `Auth token received: ${data.token.substring(0, 30)}...`, 'success');
                
            } catch (error) {
                log('authResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testAuthenticationForsa() {
            clearLog('authResult');
            log('authResult', 'Testing authentication with Forsa config...', 'info');
            
            try {
                const response = await fetch('https://accept.paymob.com/api/auth/tokens', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_key: window.PAYMOB_CONFIG_ALT.apiKey
                    })
                });
                
                log('authResult', `Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log('authResult', `Error response: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                currentAuthToken = data.token;
                log('authResult', `Auth token received: ${data.token.substring(0, 30)}...`, 'success');
                
            } catch (error) {
                log('authResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testOrderCreation() {
            clearLog('orderResult');
            if (!currentAuthToken) {
                log('orderResult', 'Please run authentication test first', 'error');
                return;
            }
            
            log('orderResult', 'Testing order creation with standard config...', 'info');
            
            const amount = parseFloat(document.getElementById('testAmount').value);
            const amountCents = Math.round(amount * 100);
            
            try {
                const response = await fetch('https://accept.paymob.com/api/ecommerce/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        auth_token: currentAuthToken,
                        delivery_needed: "false",
                        amount_cents: amountCents,
                        currency: "EGP",
                        items: [{
                            name: "Test Service",
                            amount_cents: amountCents,
                            description: "Test payment for debugging",
                            quantity: 1
                        }]
                    })
                });
                
                log('orderResult', `Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const data = await response.json();
                
                if (!response.ok) {
                    log('orderResult', `Error response: ${JSON.stringify(data, null, 2)}`, 'error');
                    return;
                }
                
                currentOrderId = data.id;
                log('orderResult', `Order created successfully: ${data.id}`, 'success');
                log('orderResult', `Order details: ${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                log('orderResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testOrderCreationForsa() {
            clearLog('orderResult');
            if (!currentAuthToken) {
                log('orderResult', 'Please run Forsa authentication test first', 'error');
                return;
            }
            
            log('orderResult', 'Testing order creation with Forsa config...', 'info');
            
            const amount = parseFloat(document.getElementById('testAmount').value);
            const amountCents = Math.round(amount * 100);
            
            try {
                const response = await fetch('https://accept.paymob.com/api/ecommerce/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        auth_token: currentAuthToken,
                        delivery_needed: "false",
                        amount_cents: amountCents,
                        currency: "EGP",
                        items: [{
                            name: "Test Forsa Service",
                            amount_cents: amountCents,
                            description: "Test Forsa payment for debugging",
                            quantity: 1
                        }]
                    })
                });
                
                log('orderResult', `Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const data = await response.json();
                
                if (!response.ok) {
                    log('orderResult', `Error response: ${JSON.stringify(data, null, 2)}`, 'error');
                    return;
                }
                
                currentOrderId = data.id;
                log('orderResult', `Order created successfully: ${data.id}`, 'success');
                log('orderResult', `Order details: ${JSON.stringify(data, null, 2)}`, 'info');
                
            } catch (error) {
                log('orderResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testPaymentKey() {
            clearLog('paymentKeyResult');
            if (!currentAuthToken || !currentOrderId) {
                log('paymentKeyResult', 'Please run authentication and order creation tests first', 'error');
                return;
            }
            
            log('paymentKeyResult', 'Testing payment key generation with standard config...', 'info');
            
            const amount = parseFloat(document.getElementById('testAmount').value);
            const amountCents = Math.round(amount * 100);
            const phone = document.getElementById('testPhone').value;
            const name = document.getElementById('testName').value;
            
            const paymentKeyData = {
                auth_token: currentAuthToken,
                amount_cents: amountCents,
                expiration: 3600,
                order_id: currentOrderId,
                billing_data: {
                    apartment: "NA",
                    email: "<EMAIL>",
                    floor: "NA",
                    first_name: name.split(' ')[0],
                    street: "NA",
                    building: "NA",
                    phone_number: phone,
                    shipping_method: "NA",
                    postal_code: "NA",
                    city: "Cairo",
                    country: "EG",
                    last_name: name.split(' ').slice(1).join(' ') || "Test",
                    state: "Cairo"
                },
                currency: "EGP",
                integration_id: window.PAYMOB_CONFIG.integrationId,
                lock_order_when_paid: "false"
            };
            
            try {
                const response = await fetch('https://accept.paymob.com/api/acceptance/payment_keys', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paymentKeyData)
                });
                
                log('paymentKeyResult', `Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const data = await response.json();
                
                if (!response.ok) {
                    log('paymentKeyResult', `Error response: ${JSON.stringify(data, null, 2)}`, 'error');
                    return;
                }
                
                log('paymentKeyResult', `Payment key generated: ${data.token.substring(0, 30)}...`, 'success');
                log('paymentKeyResult', `Full response: ${JSON.stringify(data, null, 2)}`, 'info');
                
                // Generate iframe URL
                const iframeUrl = `https://accept.paymob.com/api/acceptance/iframes/${window.PAYMOB_CONFIG.iframeId}?payment_token=${data.token}`;
                log('paymentKeyResult', `Iframe URL: ${iframeUrl}`, 'info');
                
            } catch (error) {
                log('paymentKeyResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testPaymentKeyForsa() {
            clearLog('paymentKeyResult');
            if (!currentAuthToken || !currentOrderId) {
                log('paymentKeyResult', 'Please run Forsa authentication and order creation tests first', 'error');
                return;
            }
            
            log('paymentKeyResult', 'Testing payment key generation with Forsa config...', 'info');
            
            const amount = parseFloat(document.getElementById('testAmount').value);
            const amountCents = Math.round(amount * 100);
            const phone = document.getElementById('testPhone').value;
            const name = document.getElementById('testName').value;
            
            const paymentKeyData = {
                auth_token: currentAuthToken,
                amount_cents: amountCents,
                expiration: 3600,
                order_id: currentOrderId,
                billing_data: {
                    apartment: "NA",
                    email: "<EMAIL>",
                    floor: "NA",
                    first_name: name.split(' ')[0],
                    street: "NA",
                    building: "NA",
                    phone_number: phone,
                    shipping_method: "NA",
                    postal_code: "NA",
                    city: "Cairo",
                    country: "EG",
                    last_name: name.split(' ').slice(1).join(' ') || "Test",
                    state: "Cairo"
                },
                currency: "EGP",
                integration_id: window.PAYMOB_CONFIG_ALT.integrationId,
                lock_order_when_paid: "false",
                special_reference: `FORSA_${currentOrderId}_${Date.now()}`,
                extra: {
                    payment_method: "forsa",
                    service_name: "Test Service"
                }
            };
            
            log('paymentKeyResult', `Using integration ID: ${window.PAYMOB_CONFIG_ALT.integrationId}`, 'info');
            
            try {
                const response = await fetch('https://accept.paymob.com/api/acceptance/payment_keys', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paymentKeyData)
                });
                
                log('paymentKeyResult', `Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const data = await response.json();
                
                if (!response.ok) {
                    log('paymentKeyResult', `Error response: ${JSON.stringify(data, null, 2)}`, 'error');
                    return;
                }
                
                log('paymentKeyResult', `Payment key generated: ${data.token.substring(0, 30)}...`, 'success');
                log('paymentKeyResult', `Full response: ${JSON.stringify(data, null, 2)}`, 'info');
                
                // Generate unified checkout URL
                const callbackUrl = encodeURIComponent(window.PAYMOB_CALLBACK_URLS?.callbackUrl || `${window.location.origin}/paymob-callback.html`);
                const unifiedUrl = `https://accept.paymob.com/unifiedcheckout/?payment_token=${data.token}&redirect_url=${callbackUrl}&integration_id=${window.PAYMOB_CONFIG_ALT.integrationId}`;
                log('paymentKeyResult', `Unified checkout URL: ${unifiedUrl}`, 'info');
                
            } catch (error) {
                log('paymentKeyResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testFullFlowStandard() {
            clearLog('fullFlowResult');
            log('fullFlowResult', 'Testing full payment flow with standard config...', 'info');
            
            try {
                // Step 1: Auth
                log('fullFlowResult', 'Step 1: Getting auth token...', 'info');
                await testAuthentication();
                
                if (!currentAuthToken) {
                    log('fullFlowResult', 'Failed to get auth token', 'error');
                    return;
                }
                
                // Step 2: Order
                log('fullFlowResult', 'Step 2: Creating order...', 'info');
                await testOrderCreation();
                
                if (!currentOrderId) {
                    log('fullFlowResult', 'Failed to create order', 'error');
                    return;
                }
                
                // Step 3: Payment Key
                log('fullFlowResult', 'Step 3: Generating payment key...', 'info');
                await testPaymentKey();
                
                log('fullFlowResult', 'Full flow test completed successfully!', 'success');
                
            } catch (error) {
                log('fullFlowResult', `Full flow error: ${error.message}`, 'error');
            }
        }

        async function testFullFlowForsa() {
            clearLog('fullFlowResult');
            log('fullFlowResult', 'Testing full payment flow with Forsa config...', 'info');
            
            try {
                // Step 1: Auth
                log('fullFlowResult', 'Step 1: Getting auth token...', 'info');
                await testAuthenticationForsa();
                
                if (!currentAuthToken) {
                    log('fullFlowResult', 'Failed to get auth token', 'error');
                    return;
                }
                
                // Step 2: Order
                log('fullFlowResult', 'Step 2: Creating order...', 'info');
                await testOrderCreationForsa();
                
                if (!currentOrderId) {
                    log('fullFlowResult', 'Failed to create order', 'error');
                    return;
                }
                
                // Step 3: Payment Key
                log('fullFlowResult', 'Step 3: Generating payment key...', 'info');
                await testPaymentKeyForsa();
                
                log('fullFlowResult', 'Full Forsa flow test completed successfully!', 'success');
                
            } catch (error) {
                log('fullFlowResult', `Full flow error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
