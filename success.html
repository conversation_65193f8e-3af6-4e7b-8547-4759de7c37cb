<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>

    <title>Payment Successful - Insta Clinic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header-specific CSS to ensure proper styling when loaded dynamically */
        #header * {
            box-sizing: border-box;
        }

        #header header {
            font-family: "Montserrat", sans-serif !important;
            align-items: center;
            flex-wrap: nowrap;
            position: sticky;
            top: 0;
            margin: 0;
            padding: 12px 24px;
            display: flex;
            background-color: #00989D !important;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(6, 17, 118, .08), 0 4px 12px rgb(6, 17, 118, .08);
            z-index: 1000;
        }

        #header .container1 {
            display: flex;
        }

        #header .container1 img {
            width: 150px;
            display: flex;
            cursor: pointer;
        }

        #header nav {
            display: flex;
            flex-wrap: nowrap;
            justify-content: end;
            font-family: "Montserrat", sans-serif;
        }

        #header nav button {
            background-color: transparent !important;
            border: none !important;
            cursor: pointer;
            border-radius: 20%;
            color: #eee !important;
            padding: 10px;
            text-wrap: nowrap;
            font-family: "Montserrat", sans-serif;
            font-size: 14px;
        }

        #header nav button:hover {
            color: black !important;
            font-weight: bold;
        }

        #header .btn.active {
            color: black !important;
            font-weight: bold;
        }

        #header #menu-toggle {
            display: none;
        }

        /* Mobile hamburger menu styles */
        #header .menu-button-container {
            display: none;
            height: 30px;
            width: 30px;
            cursor: pointer;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1001;
        }

        #header .menu-button,
        #header .menu-button::before,
        #header .menu-button::after {
            display: block;
            background-color: white;
            position: absolute;
            height: 3px;
            width: 30px;
            border-radius: 3px;
            transition: transform 0.25s ease;
        }

        #header .menu-button::before {
            content: '';
            margin-top: -8px;
        }

        #header .menu-button::after {
            content: '';
            margin-top: 8px;
        }

        #header #menu-toggle:checked ~ .menu-button-container .menu-button::before {
            margin-top: 0;
            transform: rotate(45deg);
        }

        #header #menu-toggle:checked ~ .menu-button-container .menu-button {
            background: rgba(255, 255, 255, 0);
        }

        #header #menu-toggle:checked ~ .menu-button-container .menu-button::after {
            margin-top: 0;
            transform: rotate(-45deg);
        }

        #header .mobile-menu {
            position: absolute;
            top: 70px;
            left: 0;
            width: 100%;
            padding: 0;
            background-color: #00989D;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            z-index: 999;
        }

        #header .mobile-menu button {
            background-color: transparent !important;
            border: none !important;
            cursor: pointer;
            color: #eee !important;
            padding: 15px 10px;
            text-align: center;
            font-size: 16px;
            width: 100%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
            display: block;
            font-family: "Montserrat", sans-serif;
        }

        #header .mobile-menu button:last-child {
            border-bottom: none !important;
        }

        #header .mobile-menu button:hover {
            color: black !important;
            font-weight: bold;
        }

        #header #menu-toggle:checked ~ .mobile-menu {
            max-height: 300px;
        }

        @media only screen and (max-width: 767px) {
            #header nav button {
                display: none;
            }

            #header .container1 img {
                width: 120px;
            }

            #header header {
                padding: 12px 10px;
            }

            #header .menu-button-container {
                display: flex;
            }
        }

        main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        .success-container {
            background-color: white;
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .success-container:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(45deg, #00989D, #FFB103, #00989D);
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            color: rgba(0, 152, 157, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 80%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-10px) rotate(5deg); }
            50% { transform: translateY(-20px) rotate(0deg); }
            75% { transform: translateY(-10px) rotate(-5deg); }
        }

        .success-icon {
            background: linear-gradient(135deg, #00989D, #28a745);
            color: white;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 48px;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
            animation: successPulse 2s ease-in-out infinite alternate;
        }

        @keyframes successPulse {
            0% { transform: scale(1); box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3); }
            100% { transform: scale(1.05); box-shadow: 0 15px 40px rgba(40, 167, 69, 0.4); }
        }

        .success-title {
            color: #00989D;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #00989D, #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .success-message {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .booking-details {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #00989D;
        }

        .booking-details h3 {
            color: #00989D;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 152, 157, 0.1);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
        }

        .detail-value {
            color: #333;
            font-weight: 500;
        }

        .total-amount {
            color: #00989D;
            font-weight: 700;
            font-size: 18px;
        }

        .next-steps {
            background: linear-gradient(135deg, #e8f6f7, #f0f9ff);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: left;
        }

        .next-steps h4 {
            color: #00989D;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .next-steps ul {
            list-style: none;
            padding: 0;
        }

        .next-steps li {
            padding: 10px 0;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            color: #555;
            line-height: 1.5;
        }

        .next-steps li i {
            color: #00989D;
            margin-top: 2px;
            width: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid;
            cursor: pointer;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00989D, #006b73);
            color: white;
            border-color: #00989D;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #006b73, #004d52);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 152, 157, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #00989D;
            border-color: #00989D;
        }

        .btn-secondary:hover {
            background: #00989D;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 152, 157, 0.2);
        }

        .success-animation {
            width: 100px;
            height: 100px;
            margin: 0 auto 30px;
        }

        @media (max-width: 768px) {
            .success-container {
                padding: 40px 20px;
                margin: 20px;
            }

            .success-title {
                font-size: 24px;
            }

            .success-message {
                font-size: 16px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                justify-content: center;
                max-width: 250px;
            }
        }
    </style>
</head>

<body>
    <!-- Load header dynamically -->
    <div id="header"></div>

    <main>
        <div class="success-container">
            <div class="floating-elements">
                <i class="floating-element fas fa-check-circle fa-2x"></i>
                <i class="floating-element fas fa-heart fa-lg"></i>
                <i class="floating-element fas fa-star fa-lg"></i>
            </div>

            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>

            <h1 class="success-title">Payment Successful!</h1>
            <p class="success-message">
                Thank you for your payment! Your appointment has been confirmed and our team will contact you shortly with further details.
            </p>

            <div class="booking-details" id="bookingDetails">
                <h3><i class="fas fa-receipt"></i> Booking Summary</h3>
                <div class="detail-row">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value" id="transactionId">Loading...</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Service:</span>
                    <span class="detail-value" id="serviceName">Loading...</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Patient Name:</span>
                    <span class="detail-value" id="patientName">Loading...</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Appointment Date:</span>
                    <span class="detail-value" id="appointmentDate">Loading...</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Amount Paid:</span>
                    <span class="detail-value total-amount" id="amountPaid">Loading...</span>
                </div>
            </div>

            <div class="next-steps">
                <h4><i class="fas fa-list-check"></i> What's Next?</h4>
                <ul>
                    <li><i class="fas fa-phone"></i> Our team will call you within 1 hour to confirm your appointment</li>
                    <li><i class="fas fa-envelope"></i> You'll receive a confirmation email with all details</li>
                    <li><i class="fas fa-calendar-check"></i> Be ready at your scheduled time - our professional will arrive punctually</li>
                    <li><i class="fas fa-headset"></i> Contact us anytime if you have questions</li>
                </ul>
            </div>

            <div class="action-buttons">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i> Back to Home
                </a>
                <a href="Aboutus.html" class="btn btn-secondary">
                    <i class="fas fa-info-circle"></i> About Our Services
                </a>
            </div>
        </div>
    </main>

    <!-- Load footer dynamically -->
    <div id="footer"></div>

    <script>
        // Load header and footer first
        document.addEventListener('DOMContentLoaded', function() {
            // Load header
            fetch('header.html')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Header not found');
                    }
                    return response.text();
                })
                .then(data => {
                    // Remove the style tag from header content to prevent conflicts
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = data;
                    const styleTag = tempDiv.querySelector('style');
                    if (styleTag) {
                        styleTag.remove();
                    }
                    
                    document.getElementById('header').innerHTML = tempDiv.innerHTML;
                    
                    // Initialize any header scripts if needed
                    const headerScripts = document.getElementById('header').querySelectorAll('script');
                    headerScripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                    });
                })
                .catch(error => {
                    console.error('Error loading header:', error);
                    // Fallback header if main header fails to load
                    document.getElementById('header').innerHTML = `
                        <header style="background: #1a5058; color: white; padding: 1rem; text-align: center;">
                            <h1 style="margin: 0;">Insta Clinic</h1>
                        </header>
                    `;
                });

            // Load footer
            fetch('footer.html')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Footer not found');
                    }
                    return response.text();
                })
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => {
                    console.error('Error loading footer:', error);
                    // Fallback footer if main footer fails to load
                    document.getElementById('footer').innerHTML = `
                        <footer style="background: #1a5058; color: white; padding: 1rem; text-align: center; margin-top: 2rem;">
                            <p>&copy; 2024 Insta Clinic. All rights reserved.</p>
                        </footer>
                    `;
                });
        });

        // Format time function
        function formatTime(timeRange) {
            if (!timeRange || !timeRange.includes('-')) {
                return timeRange; // Return as-is if not a range
            }
            
            const [startTime, endTime] = timeRange.split('-');
            
            function formatSingleTime(time24) {
                const [hours, minutes] = time24.split(':');
                const hour = parseInt(hours);
                const ampm = hour >= 12 ? 'PM' : 'AM';
                const hour12 = hour % 12 || 12;
                const formattedHour = hour12.toString().padStart(2, '0');
                return `${formattedHour}:${minutes || '00'} ${ampm}`;
            }
            
            const formattedStart = formatSingleTime(startTime);
            const formattedEnd = formatSingleTime(endTime);
            
            return `${formattedStart} - ${formattedEnd}`;
        }

        // Load booking details when page loads
        window.onload = function() {
            loadBookingDetails();
        }

        function loadBookingDetails() {
            try {
                // Get payment result from our callback system
                const paymentResultStr = sessionStorage.getItem('paymentResult');
                
                if (paymentResultStr) {
                    const paymentResult = JSON.parse(paymentResultStr);
                    console.log('Payment result loaded:', paymentResult);
                    
                    // Update the display with payment result data
                    document.getElementById('transactionId').textContent = paymentResult.transactionId || 'N/A';
                    
                    // Update success message for pending payments
                    if (paymentResult.isPending) {
                        document.querySelector('.success-title').textContent = 'Payment Received!';
                        document.querySelector('.success-message').innerHTML = 
                            'Your payment is being processed. Your appointment has been reserved and our team will contact you once payment is confirmed.';
                    }
                    
                    // Use booking data from payment result
                    const bookingData = paymentResult.bookingData || {};
                    
                    if (bookingData.serviceName) {
                        document.getElementById('serviceName').textContent = bookingData.serviceName;
                    } else {
                        document.getElementById('serviceName').textContent = 'Medical Service';
                    }
                    
                    if (bookingData.patientName) {
                        document.getElementById('patientName').textContent = bookingData.patientName;
                    } else {
                        document.getElementById('patientName').textContent = 'Patient';
                    }
                    
                    if (bookingData.appointmentDate && bookingData.appointmentTime) {
                        const formattedDate = new Date(bookingData.appointmentDate).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                        });
                        const formattedTime = formatTime(bookingData.appointmentTime);
                        document.getElementById('appointmentDate').textContent = `${formattedDate} at ${formattedTime}`;
                    } else {
                        document.getElementById('appointmentDate').textContent = 'To be confirmed';
                    }
                    
                    if (paymentResult.amount) {
                        document.getElementById('amountPaid').textContent = `${parseFloat(paymentResult.amount).toFixed(2)} ${paymentResult.currency || 'EGP'}`;
                    } else {
                        document.getElementById('amountPaid').textContent = 'Amount not available';
                    }
                    
                    // Add payment method info if available
                    if (paymentResult.paymentMethod) {
                        const detailsContainer = document.getElementById('bookingDetails');
                        const paymentMethodRow = document.createElement('div');
                        paymentMethodRow.className = 'detail-row';
                        paymentMethodRow.innerHTML = `
                            <span class="detail-label">Payment Method:</span>
                            <span class="detail-value">${paymentResult.paymentMethod}</span>
                        `;
                        // Insert before the amount row
                        const amountRow = detailsContainer.querySelector('.detail-row:last-child');
                        detailsContainer.insertBefore(paymentMethodRow, amountRow);
                    }
                    
                    // Clean up session storage
                    sessionStorage.removeItem('paymentResult');
                    
                } else {
                    // Fallback: try to get data from URL parameters (for backward compatibility)
                    const urlParams = new URLSearchParams(window.location.search);
                    const transactionId = urlParams.get('id') || urlParams.get('transaction_id') || 'N/A';
                    
                    document.getElementById('transactionId').textContent = transactionId;
                    
                    // Try to get booking data from session storage
                    const bookingDataStr = sessionStorage.getItem('currentBooking');
                    if (bookingDataStr) {
                        const bookingData = JSON.parse(bookingDataStr);
                        loadBookingDataFallback(bookingData);
                        sessionStorage.removeItem('currentBooking');
                    } else {
                        // No data available, show generic success
                        loadGenericSuccess();
                    }
                }
                
            } catch (error) {
                console.error('Error loading booking details:', error);
                loadGenericSuccess();
            }
        }

        // Fallback function for loading booking data from old format
        function loadBookingDataFallback(bookingData) {
            if (bookingData.serviceName) {
                document.getElementById('serviceName').textContent = bookingData.serviceName;
            }
            
            if (bookingData.patientName) {
                document.getElementById('patientName').textContent = bookingData.patientName;
            }
            
            if (bookingData.appointmentDate && bookingData.appointmentTime) {
                const formattedDate = new Date(bookingData.appointmentDate).toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                const formattedTime = formatTime(bookingData.appointmentTime);
                document.getElementById('appointmentDate').textContent = `${formattedDate} at ${formattedTime}`;
            }
            
            if (bookingData.amount) {
                document.getElementById('amountPaid').textContent = `${parseFloat(bookingData.amount).toFixed(2)} EGP`;
            }
        }

        // Generic success for when no booking data is available
        function loadGenericSuccess() {
            document.getElementById('serviceName').textContent = 'Medical Service';
            document.getElementById('patientName').textContent = 'Valued Patient';
            document.getElementById('appointmentDate').textContent = 'To be confirmed by our team';
            document.getElementById('amountPaid').textContent = 'Payment received';
        }
    </script>
</body>

</html> 