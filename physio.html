<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>

    <title>Dermatology Services - Insta Clinic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
        }

        /* Hero Section Styles */
        .hero-section {
            background-color: #1a5058;
            color: white;
            padding: 80px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-cta {
            display: inline-block;
            background-color: #00989D;
            color: white;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .hero-cta:hover {
            background-color: #006868;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Service Details Section */
        .service-details {
            padding: 80px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 50px;
            color: #1a5058;
            position: relative;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #00989D;
            margin: 15px auto 0;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .service-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .service-card-content {
            padding: 25px;
        }

        .service-card-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .service-card-description {
            color: #666;
            line-height: 1.6;
        }

        /* Treatments Section */
        .treatments-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .treatments-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .treatments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .treatment-item {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .treatment-item:hover {
            transform: translateY(-10px);
        }

        .treatment-icon {
            font-size: 2.5rem;
            color: #00989D;
            margin-bottom: 20px;
        }

        .treatment-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .treatment-description {
            color: #666;
            line-height: 1.6;
        }

        /* Before After Section */
        .before-after-section {
            padding: 80px 20px;
        }

        .before-after-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .before-after-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .before-after-item {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .before-after-images {
            display: flex;
            flex-direction: column;
        }

        .before-after-images img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .before-after-label {
            background-color: #1a5058;
            color: white;
            text-align: center;
            padding: 8px 0;
            font-weight: 600;
        }

        .before-after-content {
            padding: 20px;
        }

        .before-after-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: #1a5058;
        }

        .before-after-description {
            color: #666;
            line-height: 1.6;
        }

        /* Team Section */
        .team-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .team-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .team-member {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-10px);
        }

        .team-member img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .team-member-info {
            padding: 20px;
        }

        .team-member-name {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #1a5058;
        }

        .team-member-role {
            color: #00989D;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .team-member-bio {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            background-color: #00989D;
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .cta-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background-color: white;
            color: #00989D;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background-color: #1a5058;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .service-grid,
            .treatments-grid,
            .before-after-grid,
            .team-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>

<div id="header"></div>

<body>
    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Physiotherapy Services</h1>
                <p class="hero-description">Professional and compassionate physiotherapy care for a healthy lifestyle. Our physiotherapists provide personalized care to help you achieve your fitness goals.</p>
                <button class="hero-cta">Book an Appointment</button>
            </div>
        </section>

        <!-- Service Details Section -->
        <section class="service-details">
            <h2 class="section-title">Our Physiotherapy Services</h2>
            <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                At our clinic, we offer a comprehensive range of physiotherapy services tailored to meet the unique needs of each patient. Our experienced physiotherapists utilize
            </p>

            <div class="service-grid">
                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Medical Dermatology">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Physiotherapy </h3>
                        <p class="service-card-description">Physical therapy services for individuals seeking to improve their mobility, strength, flexibility, and overall well-being.</p>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1598264294394-ba29cf557627?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Cosmetic Dermatology">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Physiotherapy2</h3>
                        <p class="service-card-description">Physical therapy services for individuals seeking to improve their mobility, strength, flexibility, and overall well-being.</p>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80" alt="Surgical Dermatology">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Physiotherapy3</h3>
                        <p class="service-card-description">Removal of skin cancers, moles, cysts, and other skin growths using advanced surgical techniques.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Treatments Section -->
        <section class="treatments-section">
            <div class="treatments-container">
                <h2 class="section-title">Specialized Treatments</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    Our dermatology clinic offers a wide range of specialized treatments using the latest technologies and techniques to address various skin concerns.
                </p>

                <div class="treatments-grid">
                    <div class="treatment-item">
                        <i class="fas fa-sun treatment-icon"></i>
                        <h3 class="treatment-title">Laser Therapy</h3>
                        <p class="treatment-description">Advanced laser treatments for skin rejuvenation, hair removal, scar reduction, and treatment of vascular lesions.</p>
                    </div>

                    <div class="treatment-item">
                        <i class="fas fa-tint treatment-icon"></i>
                        <h3 class="treatment-title">Chemical Peels</h3>
                        <p class="treatment-description">Customized chemical peels to improve skin texture, reduce fine lines, and address pigmentation issues.</p>
                    </div>

                    <div class="treatment-item">
                        <i class="fas fa-syringe treatment-icon"></i>
                        <h3 class="treatment-title">Injectables</h3>
                        <p class="treatment-description">Botox and dermal fillers to reduce wrinkles, restore volume, and enhance facial contours.</p>
                    </div>

                    <div class="treatment-item">
                        <i class="fas fa-microscope treatment-icon"></i>
                        <h3 class="treatment-title">Skin Cancer Screening</h3>
                        <p class="treatment-description">Thorough skin examinations and advanced diagnostic techniques for early detection of skin cancer.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <!-- <section class="team-section">
            <div class="team-container">
                <h2 class="section-title">Our Dermatology Team</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    Our team of board-certified dermatologists and specialized staff is dedicated to providing exceptional skin care for patients of all ages.
                </p>

                <div class="team-grid">
                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-**********-2b71ea197ec2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Dr. Emma Rodriguez">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Emma Rodriguez</h3>
                            <p class="team-member-role">Medical Dermatologist</p>
                            <p class="team-member-bio">Dr. Rodriguez specializes in treating complex skin conditions including psoriasis, eczema, and autoimmune skin disorders.</p>
                        </div>
                    </div>

                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-1622253692010-333f2da6031d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80" alt="Dr. Michael Chang">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Michael Chang</h3>
                            <p class="team-member-role">Cosmetic Dermatologist</p>
                            <p class="team-member-bio">Dr. Chang is an expert in cosmetic procedures including injectables, laser treatments, and non-surgical facial rejuvenation.</p>
                        </div>
                    </div>

                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-1594824476967-48c8b964273f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Dr. Sarah Johnson">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Sarah Johnson</h3>
                            <p class="team-member-role">Surgical Dermatologist</p>
                            <p class="team-member-bio">Dr. Johnson specializes in Mohs surgery and other advanced techniques for skin cancer removal and reconstruction.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section> -->

        <!-- CTA Section -->
        <!-- <section class="cta-section">
            <div class="cta-container">
                <h2 class="cta-title">Ready for Healthier Skin?</h2>
                <p class="cta-description">Schedule an appointment with our dermatology team today. Whether you have a medical skin concern or are interested in cosmetic procedures, we're here to help.</p>
                <a href="#" class="cta-button">Book an Appointment</a>
            </div>
        </section> -->
    </main>

    <div id="footer"></div>

    <script>
        // Function to load the header and footer
        window.onload = function loadContent() {
            // Load header
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn');

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            // Load footer
            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading footer:', error));
        }
    </script>
</body>

</html>
