<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <title>Terms and Conditions - Instaclinic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            background: #f9f9f9;
            color: #222;
        }

        .terms-container {
            max-width: 900px;
            margin: 40px auto 60px auto;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.07);
            padding: 40px 30px;
        }

        h1 {
            color: #1a5058;
            font-size: 2.3rem;
            margin-bottom: 20px;
            text-align: center;
        }

        h2 {
            color: #1a5058;
            font-size: 1.3rem;
            margin-top: 32px;
            margin-bottom: 10px;
        }

        p {
            margin-bottom: 14px;
            line-height: 1.7;
        }

        ul,
        ol {
            margin-left: 24px;
            margin-bottom: 18px;
        }

        ul li,
        ol li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .section-title {
            font-weight: bold;
            color: #1a5058;
            margin-top: 28px;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        a {
            color: #3099aa;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .terms-container {
                padding: 20px 8px;
            }
        }
    </style>
</head>
<div id="header"></div>

<body>
    <main>
        <div class="terms-container">
            <h1>Instaclinic Terms and Conditions</h1>
            <section>
                <p>
                    Welcome to Instaclinic. These Terms and Conditions ("Terms") govern your use of the Instaclinic
                    mobile application and services provided therein. By using our platform, you agree to comply with
                    these Terms.
                </p>
            </section>
            <section>
                <h2>1. Introduction</h2>
                <p>
                    Instaclinic is a mobile healthcare service offering on-demand, home-based medical
                    services across Egypt, currently available in the North Coast and Cairo. Services include
                    general medical examinations, specialized consultations, and dialysis sessions.
                    <br>
                    All medical services are provided by licensed professionals employed directly by
                    Instaclinic.
                </p>
            </section>
            <section>
                <h2>2. Eligibility</h2>
                <ul>
                    <li>The user of Instaclinic services must have full legal capacity, whether they are the recipient
                        of the service or responsible for a minor receiving the service. </li>
                </ul>
            </section>
            <section>
                <h2>3. Our Services</h2>
                <p>
                    Instaclinic provides a wide range of in-home and urgent medical services, including but not
                    limited to:
                </p>
                <ul>
                    <li>General medical examinations</li>
                    <li>Specialized consultations (e.g., dermatology, dental)</li>
                    <li>Medical procedures and follow-ups</li>
                    <li>Dialysis sessions at home</li>
                    <li>Emergency medical response and ambulance services</li>
                </ul>
                <ul>
                    <li> Our emergency services are equipped to handle urgent, non-life-threatening situations on
                        site. When necessary, Instaclinic also provides ambulance transport to the nearest
                        suitable hospital for further treatment.</li>
                </ul>
            </section>
            <section>
                <h2>4. Booking & Payment</h2>
                <ul>
                    <li>Examination fees are paid in advance through the app at the time of booking. </li>
                    <li>Service-related fees (e.g., for procedures, medications, or treatments) are billed after the
                        visit based on the services provided. </li>
                    <li>Accepted payment methods may include credit/debit cards and mobile wallets. </li>
                </ul>
            </section>
            <section>
                <h2>5. Cancellation, Rescheduling & Refund Policy</h2>
                <span class="section-title">Dialysis Sessions</span>
                <ul>
                    <li>Cancellations must be made at least 24 hours before the scheduled appointment.</li>
                    <li>Cancellations within less than 24 hours will result in a 20% charge of the service fee.</li>
                    <li>No-shows (patient unavailable at the time of the visit) will be charged 50% of the
                        total service fee</li>
                </ul>
                <span class="section-title">Other Medical Services</span>
                <ul>
                    <li>Cancellations made 2–4 hours before the scheduled appointment will receive a full
                        refund of the examination fee. </li>
                    <li>Cancellations made less than 2 hours before the appointment will receive an 80%
                        refund of the examination fee. </li>
                    <li>No-shows will be charged 50% of the examination fee.</li>
                </ul>
                <span class="section-title">Emergency Requests</span>
                <ul>
                    <li>Emergency visits are non-refundable once booked.</li>
                    <li>No-shows or cancellations for emergency services will be charged 100% of the
                        service fee with no refund due to the urgent nature of the service.</li>
                </ul>
                <p>Rescheduling is allowed once per booking and must follow the same timing conditions above.</p>
            </section>
            <section>
                <h2>6. User Responsibilities</h2>
                <ul>
                    <li>You must provide accurate personal and medical information at the time of booking.</li>
                    <li>You are responsible for ensuring your availability at the scheduled location and
                        time. </li>
                    <li>You must treat healthcare providers with respect and professionalism.</li>
                    <li>User had read and understood our terms and conditions and by using our services
                        this considering an approval form him\ her on all terms and conditions. </li>
                </ul>
            </section>
            <section>
                <h2>7. Limitation of Liability</h2>
                <ul>
                    <li>Instaclinic takes responsibility for the quality of medical care provided by its staff. However,
                        we are not liable for:</li>
                    <li>Any adverse outcomes resulting from incomplete or incorrect user information.</li>
                    <li>Medical complications arising from pre-existing conditions not disclosed during booking.</li>
                    <li>Issues outside our control, such as delays caused by force majeure or user unavailability.</li>
                </ul>
            </section>
            <section>
                <h2>8. Privacy & Data Protection</h2>
                <ul>
                    <li>Your personal and medical data is treated with strict confidentiality and is protected under
                        applicable Egyptian data privacy laws.</li>
                    <li>Data is only used for purposes directly related to service delivery, improvement, or legal
                        compliance.</li>
                    <li>For more, please review our <a href="policy.html">Privacy Policy</a>.</li>
                </ul>
            </section>
            <section>
                <h2>9. Account Suspension or Termination</h2>
                <ul>
                    <li>Instaclinic reserves the right to suspend or terminate any user account that:</li>
                    <li>Violates these Terms</li>
                    <li>Engages in fraudulent or abusive behavior</li>
                    <li>Attempts to misuse the service or harm staff</li>
                </ul>
            </section>
            <section>
                <h2>10. Changes to Terms</h2>
                <ul>
                    <li>Instaclinic may update these Terms at any time. Changes will be communicated through the app or
                    website. Continued use of the app constitutes acceptance of the revised Terms.</li>
                </ul>
            </section>
            <section>
                <h2>11. Governing Law</h2>
                <ul>
                    <li>These Terms are governed by the laws of the Arab Republic of Egypt. Any disputes shall be subject to
                    the exclusive jurisdiction of Egyptian courts.</li>
                    <li>If you have any questions, please contact our support team via the app or email us at
                    <a href="mailto:<EMAIL>"><EMAIL></a>.</li>
                </ul>
            </section>
        </div>
    </main>
    <div id="footer"></div>
    <script>
        // Function to load the header from the header.html file
        window.onload = function loadHeader() {
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn'); // make sure your buttons have this class

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading header:', error));
        }
    </script>
</body>

</html>