<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <title>Document</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        .parent {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 8px;
            justify-content: space-around;
        }

        H1 {
            padding: 15px;
            font-weight: 500;
        }

        .div1 {
            grid-column: span 4 / span 4;
            grid-row: span 2 / span 2;
            padding: 15px;
            text-wrap: wrap;
            font-weight: 500;
        }

        .div2 {
            grid-column: span 4 / span 4;
            grid-row: span 2 / span 2;
            grid-row-start: 3;
            padding: 15px;
            text-wrap: wrap;
            font-weight: 500;
        }

        .div3 {
            grid-column: span 4 / span 4;
            grid-row: span 2 / span 2;
            grid-row-start: 5;
            padding: 15px;
            text-wrap: wrap;
            font-weight: 500;
        }

        .div4 {
            grid-column: span 4 / span 4;
            grid-row: span 2 / span 2;
            grid-row-start: 7;
            padding: 15px;
            text-wrap: wrap;
            font-weight: 500;
        }

        li {
            padding: 15px;
            list-style: none;
        }
    </style>
</head>
<div id="header"></div>

<body>
    <main>

        <div class="parent2">
            <H1>Instaclinic Terms and Conditions</H1>
            <div class="div1">
                <p>
                    Welcome to Instaclinic. These Terms and Conditions ("Terms") govern your use of the Instaclinic
                    mobile application and services provided therein. By using our platform, you agree to comply with
                    these Terms.
                </p>
            </div>
            <div class="div2">
                <p>
                    1. Introduction
                    Instaclinic is a mobile healthcare service offering on-demand, home-based medical services across
                    Egypt, currently available in the North Coast and Cairo. Services include general medical
                    examinations, specialized consultations, and dialysis sessions.
                    <br>
                    All medical services are provided by licensed professionals employed directly by Instaclinic.

                </p>
            </div>
            <div class="div3">
                <p>
                    2. Eligibility
                </p>
                <ul>
                    <li>You must be 12 years or older to use Instaclinic services.</li>
                    <li>Users under 18 must have a parent or legal guardian's supervision and consent.</li>
                </ul>
                <p>3. Our Services
                    Instaclinic provides a wide range of in-home and urgent medical services, including but not limited to :
                </p>
                <ul>
                    <li>General medical examinations</li>
                    <li>Specialized consultations (e.g., dermatology, dental)</li>
                    <li>Medical procedures and follow-ups</li>
                    <li>Dialysis sessions at home</li>
                    <li>Emergency medical response and ambulance services</li>
                </ul>
                <p>
                    Our emergency services are equipped to handle urgent, non-life-threatening situations on-site. When
                    necessary, Instaclinic also provides ambulance transport to the nearest suitable hospital for further
                    treatment.
                </p>
                <p>
                    4. Booking & Payment
                </p>
                <ul>
                    <li>Examination fees are paid in advance through the app at the time of booking.</li>
                    <li>Service-related fees (e.g., for procedures, medications, or treatments) are billed after the visit based on the services provided.</li>
                    <li>Accepted payment methods may include credit/debit cards, mobile wallets, and cash.</li>
                </ul>

                <p>
                    5. Cancellation, Rescheduling & Refund Policy
                </p>
                <p style="padding-top: 10px; padding-left: 12px;">Dialysis Sessions</p>
                <ul>
                    <li>Cancellations must be made at least 24 hours before the scheduled appointment.</li>
                    <li>Cancellations within less than 24 hours will result in a 20% charge of the service fee.</li>
                    <li>No-shows (patient unavailable at the time of the visit) will be charged 50% of the total service fee.</li>
                </ul>
                <p style="padding-top: 10px; padding-left: 12px;">Other Medical Services</p>
                <ul>
                    <li>Cancellations made 2–4 hours before the scheduled appointment will receive a full refund of the examination fee.</li>
                    <li>Cancellations made less than 2 hours before the appointment will receive an 80% refund of the examination fee.</li>
                    <li>No-shows will be charged 50% of the examination fee.</li>
                    <li>Emergency Requests</li>
                    <li>Emergency visits are non-refundable once booked.</li>
                    <li>No-shows or cancellations for emergency services will be charged 100% of the service fee with no refund due to the urgent nature of the service.</li>
                </ul>
                <p>Rescheduling is allowed once per booking and must follow the same timing conditions above.</p>
                <p>
                    6. User Responsibilities
                </p>
                <ul>
                    <li>You must provide accurate personal and medical information at the time of booking.</li>
                    <li>You are responsible for ensuring your availability at the scheduled location and time.</li>
                    <li>You must treat healthcare providers with respect and professionalism.</li>
                </ul>

                <p>
                    7. Limitation of Liability
                </p>

                <ul>
                    <li>Instaclinic takes responsibility for the quality of medical care provided by its staff. However, we are not liable for:</li>
                    <li>Any adverse outcomes resulting from incomplete or incorrect user information.</li>
                    <li>Medical complications arising from pre-existing conditions not disclosed during booking.</li>
                    <li>Issues outside our control, such as delays caused by force majeure or user unavailability.</li>
                </ul>
                <p>8. Privacy & Data Protection</p>
                <ul>
                    <li>Your personal and medical data is treated with strict confidentiality and is protected under applicable Egyptian data privacy laws.</li>
                    <li>Data is only used for purposes directly related to service delivery, improvement, or legal compliance.</li>
                    <li>For more, please review our [Privacy Policy].</li>
                </ul>
                <p>9. Account Suspension or Termination</p>
                <ul>
                    <li>Instaclinic reserves the right to suspend or terminate any user account that:</li>
                    <li>Violates these Terms</li>
                    <li>Engages in fraudulent or abusive behavior</li>
                    <li>Attempts to misuse the service or harm staff</li>
                </ul>

                <p>10. Changes to Terms</p>
                <p>Instaclinic may update these Terms at any time. Changes will be communicated through the app or website. Continued use of the app constitutes acceptance of the revised Terms.</p>

                <p>11. Governing Law</p>
                <p>These Terms are governed by the laws of the Arab Republic of Egypt. Any disputes shall be subject to the exclusive jurisdiction of Egyptian courts.</p>

                <p>If you have any questions, please contact our support team via the app or email us at [<EMAIL>].</p>
            

    </main>
    <div id="footer"></div>
    <script>
        // Function to load the header from the header.html file
        window.onload = function loadHeader() {
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn'); // make sure your buttons have this class

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            // Load the header content when the page loads



            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading header:', error));



        }
    </script>
</body>

</html>