<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>

    <title>Dental Services - Insta Clinic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
        }

        /* Hero Section Styles */
        .hero-section {
            background-color: #1a5058;
            color: white;
            padding: 80px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-cta {
            display: inline-block;
            background-color: #00989D;
            color: white;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .hero-cta:hover {
            background-color: #006868;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Service Details Section */
        .service-details {
            padding: 80px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 50px;
            color: #1a5058;
            position: relative;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #00989D;
            margin: 15px auto 0;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .service-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .service-card-content {
            padding: 25px;
        }

        .service-card-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .service-card-description {
            color: #666;
            line-height: 1.6;
            font-size: 1.05rem;
            flex: 1;
        }

        .service-card-list {
            margin-top: 18px;
            padding-left: 18px;
            color: #00989D;
            font-size: 0.98rem;
        }

        /* Features Section */
        .features-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-item {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            color: #00989D;
            margin-bottom: 20px;
        }

        .feature-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        /* Team Section */
        .team-section {
            padding: 80px 20px;
        }

        .team-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .team-member {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-10px);
        }

        .team-member img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .team-member-info {
            padding: 20px;
        }

        .team-member-name {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #1a5058;
        }

        .team-member-role {
            color: #00989D;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .team-member-bio {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* FAQ Section */
        .faq-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .faq-question {
            font-size: 1.2rem;
            color: #1a5058;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .faq-answer {
            color: #666;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            background-color: #00989D;
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .cta-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background-color: white;
            color: #00989D;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background-color: #1a5058;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .service-grid,
            .features-grid,
            .team-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 1.8rem;
            }
        }
    </style>
</head>

<div id="header"></div>

<body>
    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Dental Services</h1>
                <p class="hero-description">Comprehensive dental care for a healthy, beautiful smile. Our team of
                    experienced dentists provides a wide range of services from routine check-ups to advanced
                    treatments.</p>
                <button class="hero-cta" onclick="location.href='registration.html'">Book an Appointment</button>
            </div>
        </section>

        <!-- Service Details Section -->
        <section class="service-details">
            <h2 class="section-title">Our Dental Services</h2>
            <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                Insta Clinic offers a comprehensive range of dental services to meet all your oral health needs. From
                preventive care to cosmetic procedures, our experienced team is dedicated to providing high-quality
                dental care in a comfortable environment.
            </p>

            <div class="service-grid">
                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1588776814546-1ffcf47267a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                        alt="General Dentistry">
                    <div class="service-card-content">
                        <h3 class="service-card-title">General Dentistry</h3>
                        <p class="service-card-description">Comprehensive dental check-ups, cleanings, fillings, and
                            preventive care to maintain optimal oral health and prevent dental problems.</p>
                        <ul class="service-card-list">
                            <li>Routine dental exams and cleanings</li>
                            <li>Tooth-colored fillings</li>
                            <li>Preventive sealants and fluoride treatments</li>
                            <li>Oral cancer screenings</li>
                            <li>Gum disease prevention and care</li>
                        </ul>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1606811971618-4486d14f3f99?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
                        alt="Cosmetic Dentistry">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Cosmetic Dentistry</h3>
                        <p class="service-card-description">Enhance your smile with teeth whitening, veneers, bonding,
                            and other cosmetic procedures designed to improve the appearance of your teeth.</p>
                        <ul class="service-card-list">
                            <li>Professional teeth whitening</li>
                            <li>Porcelain veneers</li>
                            <li>Cosmetic bonding</li>
                            <li>Smile makeovers</li>
                            <li>Tooth reshaping and contouring</li>
                        </ul>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1598256989800-fe5f95da9787?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                        alt="Restorative Dentistry">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Restorative Dentistry</h3>
                        <p class="service-card-description">Restore damaged or missing teeth with crowns, bridges,
                            implants, and dentures to improve both function and aesthetics.</p>
                        <ul class="service-card-list">
                            <li>Dental crowns and bridges</li>
                            <li>Dental implants</li>
                            <li>Full and partial dentures</li>
                            <li>Tooth-colored fillings and inlays</li>
                            <li>Root canal therapy</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="features-container">
                <h2 class="section-title">Why Choose Our Dental Services</h2>
                <p
                    style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    At Insta Clinic, we are committed to providing exceptional dental care in a comfortable and
                    welcoming environment. Here's what sets our dental services apart:
                </p>

                <div class="features-grid">
                    <div class="feature-item">
                        <i class="fas fa-tooth feature-icon"></i>
                        <h3 class="feature-title">Advanced Technology</h3>
                        <p class="feature-description">We utilize the latest dental technology and techniques to provide
                            efficient, precise, and comfortable treatments.</p>
                    </div>

                    <div class="feature-item">
                        <i class="fas fa-user-md feature-icon"></i>
                        <h3 class="feature-title">Experienced Team</h3>
                        <p class="feature-description">Our team of skilled dentists and dental hygienists brings years
                            of experience and continuous education to your care.</p>
                    </div>

                    <div class="feature-item">
                        <i class="fas fa-smile feature-icon"></i>
                        <h3 class="feature-title">Comfortable Environment</h3>
                        <p class="feature-description">We've designed our clinic to be a relaxing space where patients
                            feel at ease during their dental treatments.</p>
                    </div>

                    <div class="feature-item">
                        <i class="fas fa-clipboard-check feature-icon"></i>
                        <h3 class="feature-title">Personalized Care</h3>
                        <p class="feature-description">We develop customized treatment plans based on your specific
                            dental needs, goals, and preferences.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <!-- <section class="team-section">
            <div class="team-container">
                <h2 class="section-title">Our Dental Team</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    Meet our team of experienced dental professionals dedicated to providing exceptional care and creating beautiful, healthy smiles.
                </p>

                <div class="team-grid">
                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-1537368910025-700350fe46c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Dr. David Thompson">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. David Thompson</h3>
                            <p class="team-member-role">General Dentist</p>
                            <p class="team-member-bio">With over 15 years of experience, Dr. Thompson specializes in comprehensive dental care with a focus on preventive dentistry and patient education.</p>
                        </div>
                    </div>

                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-**********-2b71ea197ec2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Dr. Jennifer Lee">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Jennifer Lee</h3>
                            <p class="team-member-role">Cosmetic Dentist</p>
                            <p class="team-member-bio">Dr. Lee specializes in cosmetic dentistry, helping patients achieve beautiful smiles through advanced aesthetic procedures and treatments.</p>
                        </div>
                    </div>

                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-1622253692010-333f2da6031d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80" alt="Sarah Martinez">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Sarah Martinez</h3>
                            <p class="team-member-role">Dental Hygienist</p>
                            <p class="team-member-bio">Sarah is dedicated to preventive care and patient education, helping patients maintain optimal oral health through professional cleanings and home care guidance.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <!-- <section class="cta-section">
            <div class="cta-container">
                <h2 class="cta-title">Ready for a Healthier Smile?</h2>
                <p class="cta-description">Schedule an appointment with our dental team today. Whether you need a routine check-up or are interested in cosmetic procedures, we're here to help you achieve optimal oral health.</p>
                <a href="#" class="cta-button">Book an Appointment</a>
            </div>
        </section>  -->
    </main>

    <div id="footer"></div>

    <script>
        // Function to load the header and footer
        window.onload = function loadContent() {
            // Load header
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn');

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            // Load footer
            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading footer:', error));
        }
    </script>
</body>

</html>