<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Services - Insta Clinic</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
        }

        .hero-section {
            background-color: #1a5058;
            color: white;
            padding: 80px 20px 60px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 2.7rem;
            margin-bottom: 20px;
            font-weight: 800;
            letter-spacing: 1px;
        }

        .hero-description {
            font-size: 1.25rem;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .hero-cta {
            display: inline-block;
            background-color: #00989D;
            color: white;
            padding: 14px 36px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
        }

        .hero-cta:hover {
            background-color: #006868;
            transform: translateY(-3px) scale(1.04);
            box-shadow: 0 10px 24px rgba(0, 0, 0, 0.13);
        }

        .emergency-contact-bar {
            background: #00989D;
            color: #fff;
            padding: 18px 0;
            text-align: center;
            font-size: 1.15rem;
            font-weight: 600;
            letter-spacing: 1px;
        }

        .emergency-contact-bar a {
            color: #fff;
            text-decoration: underline;
            font-weight: bold;
            margin-left: 10px;
        }

        .service-details {
            padding: 80px 20px 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.1rem;
            margin-bottom: 50px;
            color: #1a5058;
            position: relative;
            font-weight: 700;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #00989D;
            margin: 15px auto 0;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 18px rgba(0, 0, 0, 0.11);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .service-card:hover {
            transform: translateY(-10px) scale(1.03);
            box-shadow: 0 15px 32px rgba(0, 0, 0, 0.16);
        }

        .service-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .service-card-content {
            padding: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .service-card-title {
            font-size: 1.25rem;
            margin-bottom: 15px;
            color: #1a5058;
            font-weight: 700;
        }

        .service-card-description {
            color: #666;
            line-height: 1.6;
            font-size: 1.05rem;
            flex: 1;
        }

        .service-card-list {
            margin-top: 18px;
            padding-left: 18px;
            color: #00989D;
            font-size: 0.98rem;
        }

        /* Features Section */
        .features-section {
            background-color: #f0f8f8;
            padding: 60px 20px;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-item {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            padding: 30px 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-8px);
        }

        .feature-icon {
            font-size: 2.2rem;
            color: #00989D;
            margin-bottom: 10px;
        }

        .feature-title {
            color: #1a5058;
            font-size: 1.08rem;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .feature-description {
            color: #555;
            font-size: 0.97rem;
        }

        /* Team Section */
        .team-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .team-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .team-member {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-10px);
        }

        .team-member img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .team-member-info {
            padding: 20px;
        }

        .team-member-name {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #1a5058;
        }

        .team-member-role {
            color: #00989D;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .team-member-bio {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            background-color: #00989D;
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .cta-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background-color: white;
            color: #00989D;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background-color: #1a5058;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Styles */
        @media only screen and (max-width: 900px) {
            .hero-title {
                font-size: 2rem;
            }

            .service-grid,
            .team-grid,
            .features-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 1.7rem;
            }
        }

        @media only screen and (max-width: 600px) {
            .hero-content {
                padding: 0 4px;
            }

            .service-details,
            .features-section,
            .team-section {
                padding: 40px 4px;
            }

            .cta-section {
                padding: 32px 4px;
            }
        }
    </style>
</head>

<div id="header"></div>

<body>
    <!-- Emergency Contact Bar -->
    <div class="emergency-contact-bar">
        <i class="fa-solid fa-triangle-exclamation" style="color: #FFD600;"></i>
        Emergency? Call us now:
        <a href="tel:+201033298820">01033298820</a>
        or <a href="https://wa.me/+201033298820" target="_blank">WhatsApp</a>
    </div>
    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Emergency Medical Services</h1>
                <p class="hero-description">
                    Immediate, expert care when every second counts.<br>
                    Our emergency medical team is available <b>24/7</b> to provide rapid response, advanced life support, and critical care for all medical emergencies—at your location or in your home.
                </p>
                <a class="hero-cta" href="tel:+201033298820">Call Emergency Now</a>
            </div>
        </section>

        <!-- Service Details Section -->
        <section class="service-details">
            <h2 class="section-title">Our Emergency Services</h2>
            <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                Insta Clinic provides comprehensive emergency medical services with state-of-the-art equipment and highly trained professionals ready to respond to any crisis. We ensure fast, safe, and effective care for patients of all ages.
            </p>

            <div class="service-grid">
                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1516574187841-cb9cc2ca948b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                        alt="Trauma Care">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Trauma & Accident Care</h3>
                        <p class="service-card-description">
                            Immediate treatment for serious injuries including fractures, wounds, burns, and other trauma. Our team is equipped for on-site stabilization and safe transport if needed.
                        </p>
                        <ul class="service-card-list">
                            <li>Road traffic accidents</li>
                            <li>Falls & workplace injuries</li>
                            <li>Burns & lacerations</li>
                        </ul>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEjXu97HmNn_TLUdULzzomHB0g2EDi4EGbIt8OMPOh89ryZwa_wAb56_CiHxDNKwNoNeNejKURZNXRuoD4uYvH8sTWRahHBBGWGve-bgSgyvjoVIz1BzpsVSfCoFjy6jgGM4G0625PBTZLE/s0-e90-rw/Defibrillator.jpg"
                        alt="Cardiac Emergencies">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Cardiac & Defibrillator Emergencies</h3>
                        <p class="service-card-description">
                            Advanced cardiac life support for heart attacks, arrhythmias, and cardiac arrest. Our ambulances are equipped with defibrillators and emergency medications.
                        </p>
                        <ul class="service-card-list">
                            <li>Heart attack & chest pain</li>
                            <li>Cardiac arrest & arrhythmia</li>
                            <li>On-site defibrillation</li>
                        </ul>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://tytekmedical.com/wp-content/uploads/2024/06/mixed-race-medical-team-giving-first-aid-help-ambulance-car-professional-paramedic-providing-medical-help-patient-medical-worker-checking-man-stomach-emergency-care-rescue-concept-scaled.jpg"
                        alt="Respiratory Emergencies">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Respiratory & Airway Emergencies</h3>
                        <p class="service-card-description">
                            Rapid intervention for severe asthma, allergic reactions, choking, and respiratory distress. Oxygen therapy and airway management available on-site.
                        </p>
                        <ul class="service-card-list">
                            <li>Asthma attacks</li>
                            <li>Severe allergic reactions (anaphylaxis)</li>
                            <li>Choking & airway obstruction</li>
                        </ul>
                    </div>
                </div>
                <div class="service-card">
                    <img src="https://www.jems.com/wp-content/uploads/2014/08/1408JEMSgoo-p03.jpg" alt="Overdose Emergencies">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Overdose & Poisoning Emergencies</h3>
                        <p class="service-card-description">
                            Immediate care for drug overdose, alcohol poisoning, and toxic exposures. Our team provides life-saving interventions and rapid transport to specialized care if needed.
                        </p>
                        <ul class="service-card-list">
                            <li>Drug & medication overdose</li>
                            <li>Alcohol poisoning</li>
                            <li>Snake/insect bites & toxic exposures</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="features-container">
                <h2 class="section-title">Why Choose Instaclinic Emergency?</h2>
                <div class="features-grid">
                    <div class="feature-item">
                        <span class="feature-icon"><i class="fas fa-ambulance"></i></span>
                        <h3 class="feature-title">24/7 Rapid Response</h3>
                        <p class="feature-description">
                            Our emergency team is available around the clock, ready to respond to your location within minutes.
                        </p>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon"><i class="fas fa-user-md"></i></span>
                        <h3 class="feature-title">Expert Medical Team</h3>
                        <p class="feature-description">
                            Board-certified emergency physicians, paramedics, and nurses with advanced life support training.
                        </p>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon"><i class="fas fa-heartbeat"></i></span>
                        <h3 class="feature-title">Advanced Equipment</h3>
                        <p class="feature-description">
                            Equipped ambulances and on-site kits for cardiac, trauma, and respiratory emergencies.
                        </p>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon"><i class="fas fa-hospital"></i></span>
                        <h3 class="feature-title">Seamless Transfer</h3>
                        <p class="feature-description">
                            Fast, safe transfer to the nearest hospital or specialized center when advanced care is needed.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <!-- <section class="team-section">
            <div class="team-container">
                <h2 class="section-title">Meet Our Emergency Team</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                    Our emergency care professionals are dedicated to saving lives and providing compassionate support in critical moments.
                </p>
                <div class="team-grid">
                    <div class="team-member">
                        <img src="https://images.pexels.com/photos/5452201/pexels-photo-5452201.jpeg?auto=compress&w=800&q=80"
                            alt="Dr. Hossam El Din">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Hossam El Din</h3>
                            <p class="team-member-role">Consultant Emergency Physician</p>
                            <p class="team-member-bio">Specialist in trauma, cardiac emergencies, and advanced life support. Over 15 years of experience in emergency medicine.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <img src="https://images.pexels.com/photos/6749771/pexels-photo-6749771.jpeg?auto=compress&w=800&q=80"
                            alt="Nurse Mona Khalil">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Nurse Mona Khalil</h3>
                            <p class="team-member-role">Senior Emergency Nurse</p>
                            <p class="team-member-bio">Expert in triage, trauma care, and patient support during critical situations.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <img src="https://images.pexels.com/photos/5083010/pexels-photo-5083010.jpeg?auto=compress&w=800&q=80"
                            alt="Paramedic Ahmed Fathy">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Paramedic Ahmed Fathy</h3>
                            <p class="team-member-role">Lead Paramedic</p>
                            <p class="team-member-bio">Specializes in pre-hospital emergency care, rapid response, and safe patient transport.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section> -->

        <!-- CTA Section -->
        <section class="cta-section">
            <div class="cta-container">
                <h2 class="cta-title">Need Emergency Medical Assistance?</h2>
                <p class="cta-description">
                    Our emergency medical team is available 24/7. Don't hesitate to contact us in case of a medical emergency.<br>
                    <b>Call <a href="tel:+201033298820" style="color:white;">01033298820</a> or <a href="https://wa.me/+201033298820" target="_blank" style="color:white;">WhatsApp</a> now.</b>
                </p>
                <a href="tel:+201033298820" class="cta-button">Call Emergency Now</a>
            </div>
        </section>
    </main>

    <div id="footer"></div>

    <script>
        // Function to load the header and footer
        window.onload = function loadContent() {
            // Load header
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn');

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            // Load footer
            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading footer:', error));
        }
    </script>
</body>

</html>
