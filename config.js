// Supabase Configuration
// This file now contains CLIENT-SAFE credentials only

const SUPABASE_CONFIG = {
    url: 'https://djfbbxcvkwxwpkqqyjkr.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.YYVIUX07RLhYhe7V-faBdN1n9jl7q7WnvSEFrxAboME'
};

// Export configuration for use in other files
window.SUPABASE_CONFIG = SUPABASE_CONFIG;

// PayMob Configuration - CLIENT-SAFE VALUES ONLY
// 🔒 SECURITY: API keys and HMAC secrets are now stored securely in Supabase Edge Functions

// Primary PayMob Configuration (for standard payments)
window.PAYMOB_CONFIG = {
    // Get this from PayMob Dashboard -> Settings -> Account Info
    apiKey: 'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2T1RrME16ZzNMQ0p1WVcxbElqb2lhVzVwZEdsaGJDSjkuVEprTWsyOVhTR0h4Rl9xQlRfdEUwSVB6N0VwdG93QV9IZmZ6OVhLU1RidXZfLU1RaGRnSkpkZmUwaURYSlh1Y0ZYMHgtR3g3N1RMV1loSHhIbzlnWUE=', // Replace with your API key

    // Get this from PayMob Dashboard -> Developers -> Payment Integrations
    integrationId: 4832092, // Replace with your integration ID (number)

    // Get this from PayMob Dashboard -> Developers -> iframes
    iframeId: 867108, // Replace with your iframe ID (number)

    // Get this from PayMob Dashboard -> Developers -> Payment Integrations -> HMAC
    hmacSecret: 'FEAD8BA6FD4BC27816FF0AC6E8469DAD' // Replace with your HMAC secret
};

// Forsa PayMob Configuration (for mobile number payments)
window.PAYMOB_CONFIG_ALT = {
    // Get this from PayMob Dashboard -> Settings -> Account Info
    apiKey: 'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TVRBek9URTNOQ3dpYm1GdFpTSTZJakUzTlRjMU16STJNVGN1T0RReU16QXhJbjAuX2ZmQTA3X2VYYW9JM1JtLS1Qc3hJMkdidWE3d2w5a0o4cTJVRWlLV3lrSHN4WjNiSjZleEZBTkpBamVNa1cwMjJhdzNPZTg3ZkxSZmtlOTkyNWc3UUE=', // Replace with your second API key

    // Forsa Integration ID (this is the key for mobile number flow)
    integrationId: 5238766, // Your Forsa live integration ID

    // Not needed for Forsa mobile flow, but keeping for compatibility
    iframeId: 915659, // Replace with your second iframe ID (number)

    // Use the same integration ID for Unified Checkout (Forsa mobile flow)
    ucoIntegrationId: 5238766, // Same as integrationId for Forsa

    // Get this from PayMob Dashboard -> Developers -> Payment Integrations -> HMAC
    hmacSecret: 'CBA3CBDDEC037DEA0B4BD7249A9E977E' // Replace with your second HMAC secret
};

// Payment Method Configuration
window.PAYMENT_METHODS = {
    standard: {
        id: 'standard',
        name: 'Standard Payment',
        description: 'Credit/Debit Card Payment',
        config: window.PAYMOB_CONFIG,
        icon: 'fas fa-credit-card',
        enabled: true
    },
    alternative: {
        id: 'alternative',
        name: 'Forsa',
        description: 'Pay with Forsa using your mobile number - No card required',
        config: window.PAYMOB_CONFIG_ALT,
        icon: 'fas fa-mobile-alt',
        enabled: true
    }
};
// PayMob Callback URLs Configuration
// These URLs will be used when redirecting from PayMob payment
// IMPORTANT: Set these EXACT URLs in your PayMob dashboard under Payment Integrations
window.PAYMOB_CALLBACK_URLS = {
    // Main callback handler - this should match what's configured in PayMob dashboard
    callbackUrl: window.location.origin + '/paymob-callback.html',

    // Where to redirect after processing
    successPageUrl: window.location.origin + '/success.html',
    failurePageUrl: window.location.origin + '/failure.html',

    // Alternative: You can set specific URLs if needed
    // callbackUrl: window.location.origin + '/paymob-callback.html?type=callback',
}; 
