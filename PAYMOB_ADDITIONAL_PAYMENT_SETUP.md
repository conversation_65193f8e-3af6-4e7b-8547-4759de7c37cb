# PayMob Additional Payment Option Setup Guide

## Overview
This guide explains how to set up and use the additional PayMob payment option that has been added to your Insta Clinic website. The system now supports multiple PayMob integrations, allowing you to offer different payment methods to your customers.

## What's New

### 1. Multiple Payment Methods Support
- **Standard Payment**: Your existing PayMob integration (Credit/Debit Cards)
- **Alternative Payment**: New PayMob integration for alternative payment methods (Mobile Wallets, etc.)

### 2. Enhanced User Experience
- Payment method selection UI with visual cards
- Clear descriptions of each payment option
- Responsive design that works on all devices
- "Coming Soon" badges for disabled payment methods

### 3. Improved Payment Tracking
- Payment method tracking in the database
- Enhanced callback handling for multiple integrations
- Better transaction logging and debugging

## Setup Instructions

### Step 1: Configure Your Second PayMob Integration

1. **Create a New PayMob Integration**
   - Login to your PayMob dashboard
   - Go to **Developers** → **Payment Integrations**
   - Create a new integration for alternative payment methods
   - Note down the new **Integration ID** and **HMAC Secret**

2. **Create a New PayMob iframe**
   - Go to **Developers** → **iframes**
   - Create a new iframe for the alternative payment method
   - Note down the new **iframe ID**

3. **Get Your API Key** (if different from the first one)
   - Go to **Settings** → **Account Info**
   - Copy your **API Key**

### Step 2: Update Configuration

Update your `config.js` file with the new PayMob credentials:

```javascript
// Additional PayMob Configuration (for alternative payment methods)
window.PAYMOB_CONFIG_ALT = {
    // Replace with your second API key
    apiKey: 'YOUR_SECOND_API_KEY_HERE',
    
    // Replace with your second integration ID
    integrationId: 4832093, // Your actual integration ID
    
    // Replace with your second iframe ID
    iframeId: 867109, // Your actual iframe ID
    
    // Replace with your second HMAC secret
    hmacSecret: 'YOUR_SECOND_HMAC_SECRET_HERE'
};
```

### Step 3: Enable the Alternative Payment Method

In your `config.js` file, set the alternative payment method to enabled:

```javascript
alternative: {
    id: 'alternative',
    name: 'Alternative Payment',
    description: 'Alternative Payment Methods (Mobile Wallets, etc.)',
    config: window.PAYMOB_CONFIG_ALT,
    icon: 'fas fa-mobile-alt',
    enabled: true // Change this from false to true
}
```

### Step 4: Configure Callback URLs

Set the callback URLs in your **second PayMob integration** dashboard:

**Login to PayMob Dashboard → Developers → Payment Integrations → Edit Your Second Integration**

Set ALL three callback URLs to:
```
Success URL: https://yourdomain.com/paymob-callback.html
Failure URL: https://yourdomain.com/paymob-callback.html  
Pending URL: https://yourdomain.com/paymob-callback.html
```

Replace `yourdomain.com` with your actual domain.

## How It Works

### 1. User Experience Flow

1. **Service Selection**: User selects a medical service
2. **Payment Method Selection**: User sees available payment methods and selects one
3. **Payment Processing**: System uses the selected payment method's PayMob integration
4. **Callback Handling**: Payment result is processed with method-specific tracking
5. **Success/Failure**: User is redirected to appropriate page with payment details

### 2. Technical Implementation

- **Dynamic Configuration**: Each payment method uses its own PayMob configuration
- **Unified Callback**: Single callback handler processes all payment methods
- **Enhanced Tracking**: Database stores which payment method was used
- **Flexible UI**: Payment methods can be easily enabled/disabled

## Database Updates

The system now tracks additional payment information:

```sql
-- Add these columns to your bookings table if they don't exist
ALTER TABLE bookings ADD COLUMN transaction_id VARCHAR(255);
ALTER TABLE bookings ADD COLUMN payment_method VARCHAR(50);
ALTER TABLE bookings ADD COLUMN payment_source VARCHAR(50);
ALTER TABLE bookings ADD COLUMN payment_currency VARCHAR(10);
ALTER TABLE bookings ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
```

## Customization Options

### 1. Payment Method Names and Descriptions

Edit the `PAYMENT_METHODS` configuration in `config.js`:

```javascript
window.PAYMENT_METHODS = {
    standard: {
        id: 'standard',
        name: 'Credit/Debit Card', // Customize the name
        description: 'Pay with Visa, Mastercard, or other major cards', // Customize description
        config: window.PAYMOB_CONFIG,
        icon: 'fas fa-credit-card', // Change icon
        enabled: true
    },
    alternative: {
        id: 'alternative',
        name: 'Mobile Wallets', // Customize the name
        description: 'Pay with Vodafone Cash, Orange Money, or other wallets', // Customize description
        config: window.PAYMOB_CONFIG_ALT,
        icon: 'fas fa-mobile-alt', // Change icon
        enabled: true
    }
};
```

### 2. Adding More Payment Methods

You can add additional payment methods by extending the configuration:

```javascript
window.PAYMENT_METHODS = {
    // ... existing methods ...
    
    bankTransfer: {
        id: 'bankTransfer',
        name: 'Bank Transfer',
        description: 'Direct bank transfer payment',
        config: window.PAYMOB_CONFIG_BANK, // You'd need to create this config
        icon: 'fas fa-university',
        enabled: false // Enable when ready
    }
};
```

### 3. Service-Specific Payment Methods

You can modify the code to show different payment methods based on the selected service:

```javascript
// In displayServiceDetails function, you could add:
function showPaymentMethodSection() {
    // ... existing code ...
    
    // Example: Show different methods based on service
    if (selectedService.name.includes('Emergency')) {
        // Show only standard payment for emergency services
        hideAlternativePaymentMethod();
    }
}
```

## Testing

### 1. Test the Standard Payment Method

1. Select a service
2. Choose "Standard Payment"
3. Use PayMob test card: `****************`
4. Verify successful payment and callback processing

### 2. Test the Alternative Payment Method

1. Select a service
2. Choose "Alternative Payment" (if enabled)
3. Use appropriate test credentials for your second integration
4. Verify successful payment and callback processing

### 3. Test Payment Method Selection

1. Verify payment method UI appears when service is selected
2. Test selection of different payment methods
3. Verify form validation requires payment method selection
4. Test responsive design on mobile devices

## Troubleshooting

### Common Issues

1. **Alternative Payment Method Not Showing**
   - Check if `enabled: true` in config
   - Verify payment methods are loading in browser console
   - Check for JavaScript errors

2. **Payment Method Selection Not Working**
   - Verify payment methods configuration is correct
   - Check browser console for errors
   - Ensure click event listeners are attached

3. **Payment Processing Fails**
   - Verify second PayMob integration credentials
   - Check callback URLs in PayMob dashboard
   - Verify iframe IDs are correct

4. **Database Update Errors**
   - Check if new columns exist in bookings table
   - Verify Supabase permissions
   - Check browser console for errors

### Debug Information

The system logs detailed information to help with debugging:

- Payment method selection
- PayMob API calls and responses
- Callback processing details
- Database update operations

Check the browser console for these logs during testing.

## Security Considerations

1. **API Keys**: Keep your PayMob API keys secure
2. **HMAC Verification**: Ensure HMAC secrets are properly configured
3. **HTTPS**: Use HTTPS in production for all payment operations
4. **Callback URLs**: Verify callback URLs are exactly as configured

## Support

- **PayMob Support**: [<EMAIL>](mailto:<EMAIL>)
- **PayMob Documentation**: [https://docs.paymob.com/](https://docs.paymob.com/)
- **PayMob Developer Portal**: [https://developers.paymob.com/](https://developers.paymob.com/)

## Next Steps

1. Configure your second PayMob integration
2. Update the configuration file with new credentials
3. Enable the alternative payment method
4. Test both payment methods thoroughly
5. Deploy to production with proper HTTPS setup

The additional payment option is now ready to provide your customers with more flexible payment choices while maintaining the same high-quality user experience.
