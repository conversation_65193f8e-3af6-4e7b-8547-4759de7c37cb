import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FaStethoscope, FaAmbulance, FaUserMd, FaHeartbeat } from "react-icons/fa";

export default function InstaClinicHome() {
  return (
    <div className="min-h-screen bg-white text-[#006868]">
      {/* Header */}
      <header className="p-6 flex items-center justify-between shadow-md">
        <img src="/horizontal color.png" alt="InstaClinic Logo" className="h-12" />
        <nav className="space-x-6">
          <a href="#services" className="hover:text-[#00979d] font-semibold">الخدمات</a>
          <a href="#about" className="hover:text-[#00979d] font-semibold">عن التطبيق</a>
          <a href="#contact" className="hover:text-[#00979d] font-semibold">تواصل معنا</a>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="text-center py-20 bg-[#d2e221]/10">
        <h1 className="text-4xl font-bold mb-4">الرعاية الطبية أينما كنت</h1>
        <p className="text-lg text-[#00979d] mb-6">استشارات طبية - خدمات طارئة - غسيل كلى في المنزل</p>
        <Button className="bg-[#ffb203] text-white px-6 py-3 rounded-xl shadow-md hover:bg-[#ff5e00]">احجز الآن</Button>
      </section>

      {/* Services */}
      <section id="services" className="py-16 px-6 bg-white">
        <h2 className="text-3xl font-bold text-center mb-12">خدماتنا</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <FaStethoscope className="text-4xl text-[#006868] mx-auto mb-4" />
              <h3 className="text-xl font-semibold">فحوصات طبية</h3>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <FaUserMd className="text-4xl text-[#00979d] mx-auto mb-4" />
              <h3 className="text-xl font-semibold">استشارات تخصصية</h3>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <FaHeartbeat className="text-4xl text-[#d2e221] mx-auto mb-4" />
              <h3 className="text-xl font-semibold">غسيل كلى بالمنزل</h3>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <FaAmbulance className="text-4xl text-[#ff5e00] mx-auto mb-4" />
              <h3 className="text-xl font-semibold">خدمات طوارئ وإسعاف</h3>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="bg-[#00979d]/10 py-20 px-6 text-center">
        <h2 className="text-3xl font-bold mb-6">عن إنستاكلينيك</h2>
        <p className="max-w-3xl mx-auto text-lg">
          إنستاكلينيك هو تطبيق طبي مبتكر يوفر خدمات طبية منزلية وفورية في مناطق الساحل الشمالي والقاهرة. نوفر أطباء متخصصين، خدمات طوارئ، وغسيل كلى بالمنزل من خلال طاقم طبي محترف يتم توظيفه مباشرةً من قبلنا.
        </p>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-6 text-center">
        <h2 className="text-3xl font-bold mb-6">تواصل معنا</h2>
        <p className="text-lg mb-4">لأي استفسارات أو دعم، يرجى التواصل عبر البريد الإلكتروني:</p>
        <a href="mailto:<EMAIL>" className="text-[#006868] font-semibold underline"><EMAIL></a>
      </section>

      {/* Footer */}
      <footer className="bg-[#006868] text-white text-center p-4 mt-12">
        © 2025 InstaClinic. جميع الحقوق محفوظة.
      </footer>
    </div>
  );
}