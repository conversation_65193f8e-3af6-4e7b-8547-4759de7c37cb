<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <title>Document</title>
    <style>
         * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        .email-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.email-form input,
.email-form textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-family: inherit;
}

.email-form button {
  padding: 10px;
  background-color: #00989D;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.email-form button:hover {
  background-color: #007a7e;
}

    </style>
</head>
<div id="header"></div>
<body>
    <main>
        <form action="send.php" method="POST" class="email-form">
            <label for="name">Name:</label>
            <input type="text" name="name" required>
          
            <label for="email">Email:</label>
            <input type="email" name="email" required>
          
            <label for="message">Message:</label>
            <textarea name="message" rows="5" required></textarea>
          
            <button type="submit">Send Message</button>
          </form>
    </main>
   <div id="footer"></div>
    <script>
        // Function to load the header from the header.html file
        window.onload = function loadHeader() {
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn'); // make sure your buttons have this class

                buttons.forEach(btn => {
                    const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                    if (btnHref && path.endsWith(btnHref)) {
                        btn.classList.add('active');
                    } else {
                        btn.classList.remove('active');
                    }
                });
            })
                .catch(error => console.error('Error loading header:', error));
                
                // Load the header content when the page loads
                
                
                
                fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading header:', error));
                
            }
    </script>
</body>
</html>