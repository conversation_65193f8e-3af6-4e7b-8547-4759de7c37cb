<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orthopedic Services - Insta Clinic</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
        }

        .hero-section {
            background-color: #1a5058;
            color: white;
            padding: 80px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-cta {
            display: inline-block;
            background-color: #00989D;
            color: white;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .hero-cta:hover {
            background-color: #006868;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .service-details {
            padding: 80px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 50px;
            color: #1a5058;
            position: relative;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #00989D;
            margin: 15px auto 0;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .service-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .service-card-content {
            padding: 25px;
        }

        .service-card-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .service-card-description {
            color: #666;
            line-height: 1.6;
            font-size: 1.05rem;
            flex: 1;
        }
        .service-card-list {
            margin-top: 18px;
            padding-left: 18px;
            color: #00989D;
            font-size: 0.98rem;
        }

        .treatments-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .treatments-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .treatments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .treatment-item {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .treatment-item:hover {
            transform: translateY(-10px);
        }

        .treatment-icon {
            font-size: 2.2rem;
            color: #00989D;
            margin-bottom: 10px;
        }

        .treatment-title {
            color: #1a5058;
            font-size: 1.08rem;
            margin-bottom: 8px;
        }

        .treatment-description {
            color: #555;
            font-size: 0.97rem;
        }

        .approach-section {
            padding: 80px 20px;
        }

        .approach-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .approach-content {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 50px;
            margin-top: 40px;
        }

        .approach-image {
            flex: 1;
            min-width: 300px;
        }

        .approach-image img {
            width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .approach-text {
            flex: 1;
            min-width: 300px;
        }

        .approach-text h3 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #1a5058;
        }

        .approach-text p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .approach-list {
            list-style: none;
        }

        .approach-list li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            color: #666;
            line-height: 1.6;
        }

        .approach-list li:before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: #00989D;
            position: absolute;
            left: 0;
        }

        .team-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .team-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .team-member {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-10px);
        }

        .team-member img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .team-member-info {
            padding: 20px;
        }

        .team-member-name {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #1a5058;
        }

        .team-member-role {
            color: #00989D;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .team-member-bio {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .cta-section {
            background-color: #00989D;
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .cta-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background-color: white;
            color: #00989D;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background-color: #1a5058;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        @media only screen and (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .service-grid,
            .treatments-grid,
            .team-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .approach-content {
                flex-direction: column;
            }
        }
    </style>
</head>

<div id="header"></div>

<body>
    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Orthopedic Services</h1>
                <p class="hero-description">
                    Comprehensive orthopedic care for bones, joints, muscles, and ligaments. Our expert orthopedic team
                    at Insta Clinic provides advanced diagnostics, treatment, and rehabilitation for injuries and
                    chronic conditions, helping you regain mobility and live pain-free.
                </p>
                <button class="hero-cta" onclick="location.href='registration.html'">Book an Orthopedic Appointment</button>
            </div>
        </section>

        <!-- Service Details Section -->
        <section class="service-details">
            <h2 class="section-title">Our Orthopedic Services</h2>
            <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                Insta Clinic offers a full spectrum of orthopedic services, from sports injuries and trauma care to
                joint replacement and pediatric orthopedics. We use the latest techniques and technology to ensure the
                best outcomes for every patient.
            </p>

            <div class="service-grid">
                <div class="service-card">
                    <img src="https://www.cnos.net/wp-content/uploads/2022/12/iStock-**********.jpg"
                        alt="Fracture & Trauma Care">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Fracture & Trauma Care</h3>
                        <p class="service-card-description">
                            Immediate assessment and management of fractures, sprains, and musculoskeletal injuries,
                            including casting, splinting, and surgical intervention when needed.
                        </p>
                        <ul class="service-card-list">
                            <li>On-site fracture assessment and stabilization</li>
                            <li>Splinting and casting for broken bones</li>
                            <li>Management of sprains and soft tissue injuries</li>
                            <li>Surgical repair for complex fractures</li>
                            <li>Follow-up care and rehabilitation</li>
                        </ul>
                    </div>
                </div>
                <!-- <div class="service-card">
                    <img src="https://centralazortho.com/wp-content/uploads/2018/08/Total-Joint-Replacement-Reconstruction-Woman-Grabbing-Knee-1024x684.jpg"
                        alt="Joint Replacement">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Joint Replacement & Reconstruction</h3>
                        <p class="service-card-description">
                            Advanced surgical solutions for hip, knee, and shoulder replacement, as well as minimally
                            invasive arthroscopic procedures for joint repair.
                        </p>
                        <ul class="service-card-list">
                            <li>Hip, knee, and shoulder replacement</li>
                            <li>Minimally invasive arthroscopy</li>
                            <li>Joint reconstruction and resurfacing</li>
                            <li>Post-surgical rehabilitation</li>
                            <li>Pain management and mobility restoration</li>
                        </ul>
                    </div>
                </div> -->
                <div class="service-card">
                    <img src="https://glyraorthopaedics.com/wp-content/uploads/2024/02/Paediatricortho.jpg"
                        alt="Pediatric Orthopedics">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Pediatric Orthopedics</h3>
                        <p class="service-card-description">
                            Specialized care for children with bone, joint, or growth disorders, including congenital
                            deformities, scoliosis, and sports injuries.
                        </p>
                        <ul class="service-card-list">
                            <li>Treatment of congenital bone deformities</li>
                            <li>Management of scoliosis and spinal disorders</li>
                            <li>Growth plate injury care</li>
                            <li>Pediatric fracture management</li>
                            <li>Sports injury prevention and treatment</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Treatments Section -->
        <section class="treatments-section">
            <div class="treatments-container">
                <h2 class="section-title">Common Orthopedic Conditions We Treat</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                    Our orthopedic specialists diagnose and manage a wide range of musculoskeletal conditions for all
                    ages.
                </p>
                <div class="treatments-grid">
                    <div class="treatment-item">
                        <span class="treatment-icon"><i class="fas fa-bone"></i></span>
                        <h3 class="treatment-title">Bone Fractures & Dislocations</h3>
                        <p class="treatment-description">
                            Expert care for simple and complex fractures, dislocations, and trauma-related injuries.
                        </p>
                    </div>
                    <div class="treatment-item">
                        <span class="treatment-icon"><i class="fas fa-running"></i></span>
                        <h3 class="treatment-title">Sports Injuries</h3>
                        <p class="treatment-description">
                            Treatment for ligament tears, tendon injuries, sprains, strains, and overuse injuries.
                        </p>
                    </div>
                    <div class="treatment-item">
                        <span class="treatment-icon"><i class="fas fa-child"></i></span>
                        <h3 class="treatment-title">Pediatric Orthopedic Disorders</h3>
                        <p class="treatment-description">
                            Management of congenital deformities, growth plate injuries, and scoliosis in children.
                        </p>
                    </div>
                    <div class="treatment-item">
                        <span class="treatment-icon"><i class="fas fa-wheelchair"></i></span>
                        <h3 class="treatment-title">Joint Pain & Arthritis</h3>
                        <p class="treatment-description">
                            Diagnosis and treatment of osteoarthritis, rheumatoid arthritis, and chronic joint pain.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Approach Section -->
        <section class="approach-section">
            <div class="approach-container">
                <h2 class="section-title">Our Approach to Orthopedic Care</h2>
                <div class="approach-content">
                    <div class="approach-image">
                        <img src="https://dearbornassoc.com/wp-content/uploads/sites/274/2025/04/shutterstock_2478544949.jpg.optimal.jpg"
                            alt="Orthopedic Approach">
                    </div>
                    <div class="approach-text">
                        <h3>Personalized, Patient-Centered Treatment</h3>
                        <p>
                            We believe in a holistic, multidisciplinary approach to orthopedic care. Our team works
                            closely with you to develop a customized treatment plan, from diagnosis to rehabilitation.
                        </p>
                        <ul class="approach-list">
                            <li>Comprehensive evaluation and digital imaging</li>
                            <li>Non-surgical and surgical treatment options</li>
                            <li>Physical therapy and rehabilitation programs</li>
                            <li>Pain management and minimally invasive procedures</li>
                            <li>Patient education and ongoing support</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <!-- <section class="team-section">
            <div class="team-container">
                <h2 class="section-title">Our Orthopedic Team</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                    Our board-certified orthopedic surgeons and rehabilitation specialists are dedicated to your
                    recovery and well-being.
                </p>
                <div class="team-grid">
                    <div class="team-member">
                        <img src="https://images.pexels.com/photos/5452201/pexels-photo-5452201.jpeg?auto=compress&w=800&q=80"
                            alt="Dr. Hossam El Din">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Hossam El Din</h3>
                            <p class="team-member-role">Consultant Orthopedic Surgeon</p>
                            <p class="team-member-bio">Specialist in trauma, joint replacement, and minimally invasive
                                orthopedic surgery.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <img src="https://images.pexels.com/photos/6749771/pexels-photo-6749771.jpeg?auto=compress&w=800&q=80"
                            alt="Dr. Mona Khalil">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Mona Khalil</h3>
                            <p class="team-member-role">Pediatric Orthopedic Specialist</p>
                            <p class="team-member-bio">Expert in pediatric bone and joint disorders, growth
                                abnormalities, and scoliosis.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <img src="https://images.pexels.com/photos/5083010/pexels-photo-5083010.jpeg?auto=compress&w=800&q=80"
                            alt="Dr. Ahmed Fathy">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Ahmed Fathy</h3>
                            <p class="team-member-role">Rehabilitation Specialist</p>
                            <p class="team-member-bio">Focuses on post-surgical rehab, sports injury recovery, and pain
                                management.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section> -->

        <!-- CTA Section -->
        <!-- <section class="cta-section">
            <div class="cta-container">
                <h2 class="cta-title">Regain Your Mobility with Insta Clinic</h2>
                <p class="cta-description">
                    Our orthopedic specialists are here to help you recover from injury, manage chronic pain, and return
                    to your active lifestyle. Book your appointment today!
                </p>
                <a href="#" class="cta-button">Book Now</a>
            </div>
        </section> -->
    </main>

    <div id="footer"></div>

    <script>
        // Function to load the header and footer
        window.onload = function loadContent() {
            // Load header
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn');

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            // Load footer
            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading footer:', error));
        }
    </script>
</body>

</html>