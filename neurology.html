<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Bytesized&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js"></script>

    <title>Neurology Services - Insta Clinic</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-family: "Montserrat", sans-serif;
        }

        body {
            min-width: unset;
            background-color: #f9f9f9;
        }

        /* Hero Section Styles */
        .hero-section {
            background-color: #1a5058;
            color: white;
            padding: 80px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-cta {
            display: inline-block;
            background-color: #00989D;
            color: white;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .hero-cta:hover {
            background-color: #006868;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Service Details Section */
        .service-details {
            padding: 80px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 50px;
            color: #1a5058;
            position: relative;
        }

        .section-title:after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background-color: #00989D;
            margin: 15px auto 0;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .service-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .service-card-content {
            padding: 25px;
        }

        .service-card-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .service-card-description {
            color: #666;
            line-height: 1.6;
        }

        /* Technology Section */
        .technology-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .technology-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .technology-content {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 50px;
            margin-top: 40px;
        }

        .technology-image {
            flex: 1;
            min-width: 300px;
        }

        .technology-image img {
            width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .technology-text {
            flex: 1;
            min-width: 300px;
        }

        .technology-text h3 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #1a5058;
        }

        .technology-text p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .technology-list {
            list-style: none;
        }

        .technology-list li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
            color: #666;
            line-height: 1.6;
        }

        .technology-list li:before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: #00989D;
            position: absolute;
            left: 0;
        }

        /* Conditions Section */
        .conditions-section {
            padding: 80px 20px;
        }

        .conditions-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .conditions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .condition-item {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .condition-item:hover {
            transform: translateY(-10px);
        }

        .condition-icon {
            font-size: 2.5rem;
            color: #00989D;
            margin-bottom: 20px;
        }

        .condition-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #1a5058;
        }

        .condition-description {
            color: #666;
            line-height: 1.6;
        }

        /* Team Section */
        .team-section {
            background-color: #f0f8f8;
            padding: 80px 20px;
        }

        .team-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .team-member {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-10px);
        }

        .team-member img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .team-member-info {
            padding: 20px;
        }

        .team-member-name {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #1a5058;
        }

        .team-member-role {
            color: #00989D;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .team-member-bio {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            background-color: #00989D;
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .cta-description {
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            background-color: white;
            color: #00989D;
            padding: 12px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background-color: #1a5058;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .service-grid,
            .conditions-grid,
            .team-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .technology-content {
                flex-direction: column;
            }
        }
    </style>
</head>

<div id="header"></div>

<body>
    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Neurology Services</h1>
                <p class="hero-description">Comprehensive care for neurological conditions by experienced specialists. Our team provides advanced diagnostics and personalized treatment plans for disorders of the brain, spine, and nervous system.</p>
                <button class="hero-cta">Schedule a Consultation</button>
            </div>
        </section>

        <!-- Service Details Section -->
        <section class="service-details">
            <h2 class="section-title">Our Neurology Services</h2>
            <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6;">
                Insta Clinic offers comprehensive neurological care with a focus on accurate diagnosis and effective treatment of disorders affecting the brain, spinal cord, and nervous system.
            </p>

            <div class="service-grid">
                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Diagnostic Services">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Diagnostic Services</h3>
                        <p class="service-card-description">Comprehensive neurological evaluations, advanced imaging, and specialized tests to accurately diagnose neurological conditions.</p>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1579684385127-1ef15d508118?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80" alt="Treatment Plans">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Treatment Plans</h3>
                        <p class="service-card-description">Personalized treatment strategies combining medication management, therapy, and lifestyle modifications for optimal neurological health.</p>
                    </div>
                </div>

                <div class="service-card">
                    <img src="https://images.unsplash.com/photo-1584515979956-d9f6e5d09982?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Rehabilitation Services">
                    <div class="service-card-content">
                        <h3 class="service-card-title">Rehabilitation Services</h3>
                        <p class="service-card-description">Specialized neuro-rehabilitation programs to help patients recover function and improve quality of life after neurological injuries or conditions.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technology Section -->
        <section class="technology-section">
            <div class="technology-container">
                <h2 class="section-title">Advanced Neurological Technology</h2>
                <div class="technology-content">
                    <div class="technology-image">
                        <img src="https://images.unsplash.com/photo-1530026186672-2cd00ffc50fe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Neurological Technology">
                    </div>
                    <div class="technology-text">
                        <h3>State-of-the-Art Diagnostic Tools</h3>
                        <p>At Insta Clinic, we utilize the latest neurological technology to provide accurate diagnoses and effective treatments for our patients. Our advanced equipment allows us to visualize the brain and nervous system in detail, monitor neurological function, and deliver precise treatments.</p>
                        <ul class="technology-list">
                            <li>Advanced neuroimaging (MRI, CT, PET scans)</li>
                            <li>Electroencephalography (EEG) for brain activity monitoring</li>
                            <li>Electromyography (EMG) for nerve and muscle function assessment</li>
                            <li>Transcranial magnetic stimulation (TMS) for treatment</li>
                            <li>Computerized cognitive assessment tools</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Conditions Section -->
        <section class="conditions-section">
            <div class="conditions-container">
                <h2 class="section-title">Neurological Conditions We Treat</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    Our neurology team specializes in diagnosing and treating a wide range of neurological disorders affecting the brain, spinal cord, and nervous system.
                </p>

                <div class="conditions-grid">
                    <div class="condition-item">
                        <i class="fas fa-brain condition-icon"></i>
                        <h3 class="condition-title">Headache Disorders</h3>
                        <p class="condition-description">Diagnosis and management of migraines, tension headaches, cluster headaches, and other headache disorders.</p>
                    </div>

                    <div class="condition-item">
                        <i class="fas fa-bolt condition-icon"></i>
                        <h3 class="condition-title">Epilepsy & Seizures</h3>
                        <p class="condition-description">Comprehensive evaluation and treatment of epilepsy and other seizure disorders with medication and advanced therapies.</p>
                    </div>

                    <div class="condition-item">
                        <i class="fas fa-hand-tremor condition-icon"></i>
                        <h3 class="condition-title">Movement Disorders</h3>
                        <p class="condition-description">Treatment for Parkinson's disease, essential tremor, dystonia, and other movement disorders.</p>
                    </div>

                    <div class="condition-item">
                        <i class="fas fa-memory condition-icon"></i>
                        <h3 class="condition-title">Memory Disorders</h3>
                        <p class="condition-description">Evaluation and management of Alzheimer's disease, dementia, and other cognitive disorders.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <section class="team-section">
            <div class="team-container">
                <h2 class="section-title">Our Neurology Team</h2>
                <p style="text-align: center; max-width: 800px; margin: 0 auto; color: #666; line-height: 1.6; margin-bottom: 40px;">
                    Our team of board-certified neurologists and specialized staff is dedicated to providing exceptional care for patients with neurological conditions.
                </p>

                <div class="team-grid">
                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Dr. Robert Chen">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Robert Chen</h3>
                            <p class="team-member-role">Chief Neurologist</p>
                            <p class="team-member-bio">With over 20 years of experience, Dr. Chen specializes in movement disorders and neurodegenerative diseases, bringing advanced expertise to complex neurological cases.</p>
                        </div>
                    </div>

                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-1594824476967-48c8b964273f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Dr. Sophia Williams">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. Sophia Williams</h3>
                            <p class="team-member-role">Epilepsy Specialist</p>
                            <p class="team-member-bio">Dr. Williams specializes in epilepsy and seizure disorders, offering advanced diagnostic evaluations and comprehensive treatment plans.</p>
                        </div>
                    </div>

                    <div class="team-member">
                        <img src="https://images.unsplash.com/photo-**********-2b71ea197ec2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Dr. James Peterson">
                        <div class="team-member-info">
                            <h3 class="team-member-name">Dr. James Peterson</h3>
                            <p class="team-member-role">Neuro-Rehabilitation Specialist</p>
                            <p class="team-member-bio">Dr. Peterson focuses on neuro-rehabilitation, helping patients recover function and improve quality of life after strokes, injuries, and other neurological conditions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="cta-section">
            <div class="cta-container">
                <h2 class="cta-title">Need Neurological Care?</h2>
                <p class="cta-description">Our team of neurologists is ready to provide expert care for your neurological concerns. Contact us today to schedule a consultation.</p>
                <a href="#" class="cta-button">Schedule a Consultation</a>
            </div>
        </section>
    </main>

    <div id="footer"></div>

    <script>
        // Function to load the header and footer
        window.onload = function loadContent() {
            // Load header
            fetch('header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header').innerHTML = data;
                    const path = window.location.pathname;
                    const buttons = document.querySelectorAll('.btn');

                    buttons.forEach(btn => {
                        const btnHref = btn.getAttribute('onclick')?.match(/'(.*?)'/)?.[1];
                        if (btnHref && path.endsWith(btnHref)) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                })
                .catch(error => console.error('Error loading header:', error));

            // Load footer
            fetch('footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer').innerHTML = data;
                })
                .catch(error => console.error('Error loading footer:', error));
        }
    </script>
</body>

</html>
