<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
  <defs>
    <style>
      .cls-1 {
        stroke: #ffb203;
      }

      .cls-1, .cls-2 {
        fill: none;
        stroke-miterlimit: 10;
        stroke-width: 2.83px;
      }

      .cls-2 {
        stroke: #fff;
      }

      .cls-3 {
        fill: #006868;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <circle class="cls-3" cx="26" cy="26" r="26"/>
    <g>
      <circle class="cls-1" cx="26" cy="16.94" r="8.16"/>
      <path class="cls-2" d="M23.15,30.53h5.69c5.18,0,9.39,4.21,9.39,9.39v.27c0,1.67-1.36,3.03-3.03,3.03H16.8c-1.67,0-3.03-1.36-3.03-3.03v-.27c0-5.18,4.21-9.39,9.39-9.39Z"/>
    </g>
  </g>
</svg>